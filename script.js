// 数据存储
let tasks = JSON.parse(localStorage.getItem('ganttTasks')) || [];
// 确保所有任务都有颜色属性
if (tasks.length > 0 && !tasks[0].color) {
    tasks = tasks.map(task => ({
        ...task,
        color: '#e74c3c' // 默认颜色
    }));
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));
}
let groups = JSON.parse(localStorage.getItem('ganttGroups')) || ['前期', '幕墙施工', '后期'];
let selectedTaskId = null;

// 边框设置相关变量 - 移到文件开头避免初始化顺序问题
let borderSettings = JSON.parse(localStorage.getItem('ganttBorderSettings')) || {
    borderColor: '#ffffff',
    borderWidth: 1,
    taskHeight: 45, // 固定行高45px
    taskOpacity: 100,
    transparentBorder: false
};

// 固定的行高和任务条高度常量
const FIXED_ROW_HEIGHT = 45; // 行高
const HEIGHT_MARGIN = 10; // 行高与任务条高度的固定差值
const FIXED_TASK_BAR_HEIGHT = FIXED_ROW_HEIGHT - HEIGHT_MARGIN; // 任务条高度 = 行高 - 固定差值

// 拖拽相关变量
let isDragging = false;
let draggedTask = null;
let draggedTaskId = null;
let dragStartX = 0;
let dragStartY = 0;
let dragOffsetX = 0;
let dragOffsetY = 0;
let dropIndicator = null;
let timeDropIndicator = null;

// 高度对齐相关变量
let alignmentAnimationFrame = null;

// 获取当前步长天数
function getStepDays() {
    const stepValue = parseInt(customStep.value) || 1;
    return Math.max(1, stepValue);
}

// 新的统一行高计算函数 - 基于用户需求的固定公式
function calculateFixedRowHeight() {
    // 修正：行高就是任务条高度，边框是内部的，不影响行高
    const taskHeight = borderSettings.taskHeight || 45;
    const borderWidth = borderSettings.borderWidth || 1;
    const rowHeight = taskHeight; // 行高就是任务条高度
    const taskBarHeight = taskHeight - (borderWidth * 2); // 任务条高度需要减去边框

    console.log(`计算固定行高: 行高=${rowHeight}px, 任务条高度=${taskBarHeight}px (减去边框${borderWidth}px × 2)`);

    return {
        rowHeight: rowHeight,
        taskBarHeight: taskBarHeight
    };
}

// 更新行高和任务条高度的数学关系
function updateHeightRelationship(newRowHeight) {
    if (newRowHeight && newRowHeight > HEIGHT_MARGIN) {
        // 更新CSS变量
        document.documentElement.style.setProperty('--row-height', `${newRowHeight}px`);
        document.documentElement.style.setProperty('--task-bar-height', `${newRowHeight - HEIGHT_MARGIN}px`);

        // 更新borderSettings以保持一致性
        if (borderSettings) {
            borderSettings.taskHeight = newRowHeight;
        }

        return newRowHeight - HEIGHT_MARGIN;
    }
    return FIXED_TASK_BAR_HEIGHT;
}

// 防抖函数和预览缓存
let previewDebounceTimer = null;

// DOM 元素
const taskTableBody = document.getElementById('taskList');
const ganttChart = document.getElementById('ganttChart');
const timeRuler = document.getElementById('timeRuler');
const customStep = document.getElementById('customStep');
const selectAll = document.getElementById('selectAll');

// 模态框元素
const taskModal = document.getElementById('taskModal');
const groupModal = document.getElementById('groupModal');
const modalTitle = document.getElementById('modalTitle');

// 表单元素
const taskForm = document.getElementById('taskForm');
const taskId = document.getElementById('taskId');
const taskGroup = document.getElementById('taskGroup');
const taskNumber = document.getElementById('taskNumber');
const taskName = document.getElementById('taskName');
const taskDuration = document.getElementById('taskDuration');
const taskStart = document.getElementById('taskStart');
const taskEnd = document.getElementById('taskEnd');

// 按钮元素
const addTaskBtn = document.getElementById('addTaskBtn');
const editTaskBtn = document.getElementById('editTaskBtn');
const deleteTaskBtn = document.getElementById('deleteTaskBtn');
const addGroupBtn = document.getElementById('addGroupBtn');
const moveUpBtn = document.getElementById('moveUpBtn');
const moveDownBtn = document.getElementById('moveDownBtn');
const copyTaskBtn = document.getElementById('copyTaskBtn');
const batchColorBtn = document.getElementById('batchColorBtn');
const moveLeftBtn = document.getElementById('moveLeftBtn');
const moveRightBtn = document.getElementById('moveRightBtn');

// 分组管理元素
const groupName = document.getElementById('groupName');
const groupList = document.getElementById('groupList');
const addGroupSubmit = document.getElementById('addGroupSubmit');

// 初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化列设置
        initColumnSettings();
        
        // 初始化分组下拉菜单
        updateGroupOptions();
        
        // 渲染任务和甘特图
        renderTasks();
        renderGanttChart();
        
        // 设置默认开始日期为今天
        const today = new Date().toISOString().split('T')[0];
        taskStart.value = today;
        
        // 绑定事件监听器
        bindEventListeners();

        // 绑定新功能的事件监听器
        bindBatchOperationEvents();

        // 应用固定行高（新的统一函数）
        applyFixedRowHeight();
        
        // 同步甘特图主体和时间标尺的滚动
        const unifiedBody = document.querySelector('.unified-body');
        if (unifiedBody) {
            unifiedBody.addEventListener('scroll', function() {
                const timeRulerInner = document.getElementById('timeRuler');
                if (timeRulerInner) {
                    timeRulerInner.style.transform = `translateX(-${this.scrollLeft}px)`;
                }
            });
        }
    });

// 绑定批量操作事件
function bindBatchOperationEvents() {
    // 批量颜色选择模态框事件
    document.getElementById('applyBatchColor').addEventListener('click', applyBatchColor);
    document.getElementById('cancelBatchColor').addEventListener('click', closeBatchColorModal);

    // 批量移动模态框事件
    document.getElementById('applyBatchMove').addEventListener('click', applyBatchMove);
    document.getElementById('cancelBatchMove').addEventListener('click', closeBatchMoveModal);

    // 模态框点击外部关闭
    document.getElementById('batchColorModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeBatchColorModal();
        }
    });

    document.getElementById('batchMoveModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeBatchMoveModal();
        }
    });
}

    // 初始化列设置
function initColumnSettings() {
    const savedColumns = JSON.parse(localStorage.getItem('ganttColumnSettings')) || {
        group: true,
        number: true,
        name: true,
        duration: true,
        start: true,
        end: true
    };
    
    // 设置复选框初始状态
    document.getElementById('colGroup').checked = savedColumns.group;
    document.getElementById('colNumber').checked = savedColumns.number;
    document.getElementById('colName').checked = savedColumns.name;
    document.getElementById('colDuration').checked = savedColumns.duration;
    document.getElementById('colStart').checked = savedColumns.start;
    document.getElementById('colEnd').checked = savedColumns.end;
    
    // 应用列显示设置
    applyColumnSettings(savedColumns);
}

// 应用列显示设置
function applyColumnSettings(settings) {
    const columnMap = {
        group: { index: 2, name: '分组名' },
        number: { index: 3, name: '编号' },
        name: { index: 4, name: '工作名称' },
        duration: { index: 5, name: '持续时间' },
        start: { index: 6, name: '开始时间' },
        end: { index: 7, name: '结束时间' }
    };
    
    Object.keys(columnMap).forEach(key => {
        const { index } = columnMap[key];
        const isVisible = settings[key];
        
        // 隐藏/显示表头
        const headerCells = document.querySelectorAll(`#taskTable th:nth-child(${index})`);
        const dataCells = document.querySelectorAll(`#taskTableBody td:nth-child(${index})`);
        
        headerCells.forEach(cell => {
            cell.style.display = isVisible ? '' : 'none';
        });
        
        dataCells.forEach(cell => {
            cell.style.display = isVisible ? '' : 'none';
        });
    });
}

// 保存列设置
function saveColumnSettings() {
    const settings = {
        group: document.getElementById('colGroup').checked,
        number: document.getElementById('colNumber').checked,
        name: document.getElementById('colName').checked,
        duration: document.getElementById('colDuration').checked,
        start: document.getElementById('colStart').checked,
        end: document.getElementById('colEnd').checked
    };
    
    localStorage.setItem('ganttColumnSettings', JSON.stringify(settings));
    applyColumnSettings(settings);
    closeColumnModal();
}

// 打开列设置模态框
function openColumnModal() {
    document.getElementById('columnModal').style.display = 'block';
}

// 关闭列设置模态框
function closeColumnModal() {
    document.getElementById('columnModal').style.display = 'none';
}

// 新的统一固定行高应用函数 - 替换所有动态高度计算
function applyFixedRowHeight() {
    console.log('=== 应用固定行高（新的统一函数）===');

    // 使用新的固定计算公式
    const { rowHeight, taskBarHeight } = calculateFixedRowHeight();

    console.log(`应用固定行高: 行高=${rowHeight}px, 任务条高度=${taskBarHeight}px`);

    // 更新CSS变量
    document.documentElement.style.setProperty('--row-height', `${rowHeight}px`);
    document.documentElement.style.setProperty('--task-bar-height', `${taskBarHeight}px`);

    // 获取所有需要更新的元素
    const taskTableBody = document.getElementById('taskTableBody');
    const ganttChart = document.getElementById('ganttChart');
    const taskRows = document.querySelectorAll('#taskTableBody tr');
    const ganttTasks = document.querySelectorAll('.gantt-task-container');
    const ganttTaskBars = document.querySelectorAll('.gantt-task');
    const ganttTaskLabels = document.querySelectorAll('.gantt-task-label');

    // 1. 强制设置表格行高度（使用!important覆盖CSS约束）
    taskRows.forEach((row, index) => {
        row.style.setProperty('height', `${rowHeight}px`, 'important');
        row.style.setProperty('line-height', `${rowHeight}px`, 'important');
        row.style.setProperty('box-sizing', 'border-box', 'important');

        // 设置单元格高度
        const cells = row.querySelectorAll('td');
        cells.forEach(cell => {
            cell.style.setProperty('height', `${rowHeight}px`, 'important');
            cell.style.setProperty('line-height', `${rowHeight}px`, 'important');
            cell.style.setProperty('box-sizing', 'border-box', 'important');
            cell.style.setProperty('vertical-align', 'middle', 'important');
        });
    });

    // 2. 设置甘特图任务条容器
    ganttTasks.forEach((container, index) => {
        container.style.setProperty('height', `${rowHeight}px`, 'important');
        container.style.setProperty('top', `${index * rowHeight}px`, 'important');
        container.style.setProperty('position', 'absolute', 'important');
    });

    // 3. 设置甘特图任务条
    ganttTaskBars.forEach(taskBar => {
        taskBar.style.setProperty('height', `${taskBarHeight}px`, 'important');
        taskBar.style.setProperty('line-height', `${taskBarHeight}px`, 'important');
        taskBar.style.setProperty('box-sizing', 'border-box', 'important');
    });

    // 4. 设置甘特图任务标签
    ganttTaskLabels.forEach(label => {
        label.style.setProperty('height', `${taskBarHeight}px`, 'important');
        label.style.setProperty('line-height', `${taskBarHeight}px`, 'important');
        label.style.setProperty('box-sizing', 'border-box', 'important');
    });

    // 5. 设置甘特图容器高度（基于内容，不是固定容器）
    if (taskTableBody && ganttChart) {
        const realTaskCount = Array.from(taskRows).filter(row =>
            row.children.length > 6 && row.children[3].textContent.trim() !== ''
        ).length;

        // 甘特图高度 = 实际任务数 × 行高 + 少量缓冲
        const ganttHeight = realTaskCount * rowHeight + 50;
        ganttChart.style.setProperty('height', `${ganttHeight}px`, 'important');
        ganttChart.style.setProperty('min-height', `${ganttHeight}px`, 'important');

        console.log(`甘特图容器高度设置: ${realTaskCount}个任务 × ${rowHeight}px = ${ganttHeight}px`);
    }

    // 6. 更新时间标尺高度
    const timeRuler = document.getElementById('timeRuler');
    if (timeRuler) {
        timeRuler.style.setProperty('height', `${rowHeight}px`, 'important');

        const monthRow = document.querySelector('#timeRuler > .month-row');
        const dateRow = document.querySelector('#timeRuler > .date-row');

        if (monthRow && dateRow) {
            const halfHeight = rowHeight / 2;
            monthRow.style.setProperty('height', `${halfHeight}px`, 'important');
            dateRow.style.setProperty('height', `${halfHeight}px`, 'important');

            // 更新子元素高度
            const monthUnits = monthRow.querySelectorAll('.month-unit');
            const dateUnits = dateRow.querySelectorAll('.date-unit');

            monthUnits.forEach(unit => {
                unit.style.setProperty('height', `${halfHeight}px`, 'important');
                unit.style.setProperty('line-height', `${halfHeight}px`, 'important');
            });

            dateUnits.forEach(unit => {
                unit.style.setProperty('height', `${halfHeight}px`, 'important');
                unit.style.setProperty('line-height', `${halfHeight}px`, 'important');
            });
        }
    }

    console.log(`固定行高应用完成: 行高=${rowHeight}px, 任务条高度=${taskBarHeight}px`);
}

// 绑定事件监听器
function bindEventListeners() {
    // 工具栏按钮
    addTaskBtn.addEventListener('click', openAddTaskModal);
    editTaskBtn.addEventListener('click', openEditTaskModal);
    deleteTaskBtn.addEventListener('click', deleteSelectedTasks);
    addGroupBtn.addEventListener('click', openGroupModal);
    
    // 表单事件
    taskForm.addEventListener('submit', saveTask);
    document.getElementById('cancelTask').addEventListener('click', closeTaskModal);
    
    // 日期联动
    taskStart.addEventListener('change', updateEndDate);
    taskDuration.addEventListener('input', updateEndDate);
    
    // 步长输入变化
    customStep.addEventListener('input', function() {
        // 确保输入值至少为1
        let value = parseInt(customStep.value) || 1;
        value = Math.max(1, value);
        customStep.value = value;

        // 强制完全重新渲染甘特图和任务条位置
        console.log(`步长已更改为: ${value}天/格，正在重新渲染甘特图...`);

        // 重新渲染任务列表和甘特图（这会重新创建表格中的任务条）
        renderTasks();
        renderGanttChart();

        // 应用固定行高
        setTimeout(() => {
            applyFixedRowHeight();
        }, 100);
    });
    
    // 全选
    selectAll.addEventListener('change', toggleSelectAll);
    
    // 模态框关闭
    document.querySelectorAll('.modal .close').forEach(closeBtn => {
        closeBtn.addEventListener('click', closeAllModals);
    });
    
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            closeAllModals();
        }
    });
    
    // 分组管理
    addGroupSubmit.addEventListener('click', addNewGroup);
    
    // 列设置
    document.getElementById('columnSettingsBtn').addEventListener('click', openColumnModal);
    document.getElementById('saveColumns').addEventListener('click', saveColumnSettings);
    document.getElementById('cancelColumns').addEventListener('click', closeColumnModal);
    
    // 批量分组
    document.getElementById('batchGroupBtn').addEventListener('click', openBatchGroupModal);
    document.getElementById('saveBatchGroup').addEventListener('click', saveBatchGroup);
    document.getElementById('cancelBatchGroup').addEventListener('click', closeBatchGroupModal);
    
    // 批量操作按钮
    moveUpBtn.addEventListener('click', moveSelectedTasksUp);
    moveDownBtn.addEventListener('click', moveSelectedTasksDown);
    copyTaskBtn.addEventListener('click', copySelectedTasks);
    batchColorBtn.addEventListener('click', openBatchColorModal);
    moveLeftBtn.addEventListener('click', () => moveSelectedTasksByDays('left'));
    moveRightBtn.addEventListener('click', () => moveSelectedTasksByDays('right'));
}

// 更新分组选项
function updateGroupOptions() {
    taskGroup.innerHTML = '';
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        taskGroup.appendChild(option);
    });
}

// 更新结束日期
function updateEndDate() {
    if (taskStart.value && taskDuration.value) {
        const startDate = new Date(taskStart.value);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + parseInt(taskDuration.value) - 1);
        taskEnd.value = endDate.toISOString().split('T')[0];
    }
}

// 打开添加任务模态框
function openAddTaskModal() {
    modalTitle.textContent = '添加任务';
    taskForm.reset();
    taskId.value = '';
    
    // 设置默认开始日期
    const today = new Date().toISOString().split('T')[0];
    taskStart.value = today;
    
    taskModal.style.display = 'block';
}

// 打开编辑任务模态框
function openEditTaskModal() {
    if (!selectedTaskId) return;
    
    const task = tasks.find(t => t.id === selectedTaskId);
    if (!task) return;
    
    modalTitle.textContent = '编辑任务';
    taskId.value = task.id;
    taskGroup.value = task.group;
    taskName.value = task.name;
    taskDuration.value = task.duration;
    taskStart.value = task.start;
    taskEnd.value = task.end;
    document.getElementById('taskColor').value = task.color || '#e74c3c';
    
    taskModal.style.display = 'block';
}

// 关闭任务模态框
function closeTaskModal() {
    taskModal.style.display = 'none';
}

// 检查并扩展日期范围
function extendDateRangeIfNeeded() {
    if (tasks.length === 0) return;
    
    let minDate = new Date(tasks[0].start);
    let maxDate = new Date(tasks[0].end);
    
    tasks.forEach(task => {
        const taskStart = new Date(task.start);
        const taskEnd = new Date(task.end);
        
        if (taskStart < minDate) minDate = taskStart;
        if (taskEnd > maxDate) maxDate = taskEnd;
    });
    
    // 添加前后缓冲期
    minDate.setDate(minDate.getDate() - 7); // 提前7天
    maxDate.setDate(maxDate.getDate() + 7); // 延后7天
    
    return { start: minDate, end: maxDate };
}

// 保存任务
function saveTask(e) {
    e.preventDefault();
    
    const id = taskId.value || 'task_' + Date.now();
    const group = taskGroup.value;
    const name = taskName.value;
    const duration = parseInt(taskDuration.value);
    const start = taskStart.value;
    const end = taskEnd.value;
    
    if (!group || !name || duration <= 0 || !start || !end) {
        alert('请填写所有必填字段，持续时间必须大于0');
        return;
    }
    
    // 验证日期有效性
    const startDateObj = new Date(start);
    const endDateObj = new Date(end);
    
    if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        alert('请输入有效的日期');
        return;
    }
    
    if (endDateObj < startDateObj) {
        alert('结束日期不能早于开始日期');
        return;
    }
    
    const taskColor = document.getElementById('taskColor').value;
    const task = {
        id,
        group,
        name,
        duration,
        start,
        end,
        color: taskColor
    };
    
    if (taskId.value) {
        // 编辑任务
        const index = tasks.findIndex(t => t.id === taskId.value);
        if (index !== -1) {
            tasks[index] = task;
        }
    } else {
        // 添加新任务
        tasks.push(task);
    }
    
    // 保存到本地存储
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));
    
    // 重新渲染（自动调整日期范围）
    renderTasks();
    renderGanttChart();
    
    // 关闭模态框
    closeTaskModal();
}

// 渲染任务表格 - 新的统一表格方案
function renderTasks() {
    const taskTableBody = document.getElementById('taskTableBody');
    taskTableBody.innerHTML = '';

    tasks.forEach((task, index) => {
        const row = document.createElement('tr');
        row.dataset.id = task.id;
        row.dataset.index = index;
        row.style.cursor = 'grab';
        row.style.height = `${FIXED_ROW_HEIGHT}px`; /* 使用统一的固定行高 */
        
        // 自动编号，基于当前任务在所有任务中的位置
        const taskNumber = String(index + 1).padStart(3, '0');
        
        // 创建单元格 - 紧凑布局，列宽由colgroup定义
        const checkboxCell = document.createElement('td');
        checkboxCell.className = 'task-info-cell';
        checkboxCell.innerHTML = '<input type="checkbox" class="task-checkbox">';

        const groupCell = document.createElement('td');
        groupCell.className = 'task-info-cell';
        groupCell.textContent = task.group;
        groupCell.setAttribute('data-content', task.group);

        const numberCell = document.createElement('td');
        numberCell.className = 'task-info-cell';
        numberCell.textContent = taskNumber;
        numberCell.setAttribute('data-content', taskNumber);

        const nameCell = document.createElement('td');
        nameCell.className = 'task-info-cell';
        nameCell.textContent = task.name;
        nameCell.setAttribute('data-content', task.name);

        const durationCell = document.createElement('td');
        durationCell.className = 'task-info-cell';
        durationCell.textContent = task.duration;
        durationCell.setAttribute('data-content', task.duration);

        const startCell = document.createElement('td');
        startCell.className = 'task-info-cell';
        startCell.textContent = task.start;
        startCell.setAttribute('data-content', task.start);

        const endCell = document.createElement('td');
        endCell.className = 'task-info-cell';
        endCell.textContent = task.end;
        endCell.setAttribute('data-content', task.end);
        
        // 创建甘特图单元格
        const ganttCell = document.createElement('td');
        ganttCell.className = 'gantt-cell';
        ganttCell.style.position = 'relative';
        ganttCell.style.minWidth = '800px';

        // 在甘特图单元格中创建任务条
        createTaskBarInCell(ganttCell, task, index);

        row.appendChild(checkboxCell);
        row.appendChild(groupCell);
        row.appendChild(numberCell);
        row.appendChild(nameCell);
        row.appendChild(durationCell);
        row.appendChild(startCell);
        row.appendChild(endCell);
        row.appendChild(ganttCell);
        
        // 添加行点击事件（排除勾选框）
        row.addEventListener('click', function(e) {
            if (e.target.type !== 'checkbox' && !e.target.classList.contains('task-checkbox')) {
                document.querySelectorAll('#taskTableBody tr').forEach(r => r.classList.remove('selected'));
                row.classList.add('selected');
                selectedTaskId = task.id;
                updateToolbarButtons();
            }
        });
        
        // 添加复选框事件
        const checkbox = row.querySelector('.task-checkbox');
        checkbox.addEventListener('change', function(e) {
            e.stopPropagation(); // 防止事件冒泡到行
            
            if (this.checked) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
                if (selectedTaskId === task.id) {
                    selectedTaskId = null;
                }
            }
            updateToolbarButtons();
        });
        
        // 添加上下拖拽功能
        let isRowDragging = false;
        let startY;
        let originalIndex = index;
        let currentRow = row;
        
        row.addEventListener('mousedown', function(e) {
            if (e.button === 0 && e.target.type !== 'checkbox') {
                isRowDragging = true;
                startY = e.clientY;
                row.style.cursor = 'grabbing';
                document.body.style.userSelect = 'none';
                row.classList.add('selected');
                selectedTaskId = task.id;
                e.preventDefault();
            }
        });
        
        document.addEventListener('mousemove', function(e) {
            if (!isRowDragging) return;
            
            const dy = e.clientY - startY;
            const rowHeight = 50; // 固定高度
            const rowChange = Math.round(dy / rowHeight);
            let newIndex = originalIndex + rowChange;
            
            // 确保不超出任务范围
            newIndex = Math.max(0, Math.min(tasks.length - 1, newIndex));
            
            // 更新当前行的视觉位置
            const allRows = Array.from(taskTableBody.querySelectorAll('tr'));
            allRows.forEach(r => r.style.position = 'relative');
            
            if (newIndex !== originalIndex) {
                // 计算移动距离
                const moveDistance = (newIndex - originalIndex) * rowHeight;
                currentRow.style.transform = `translateY(${moveDistance}px)`;
            }
        });
        
        document.addEventListener('mouseup', function() {
            if (!isRowDragging) return;
            
            isRowDragging = false;
            row.style.cursor = 'grab';
            document.body.style.userSelect = '';
            
            // 计算新的索引
            const dy = event.clientY - startY;
            const rowHeight = 50; // 固定高度
            const rowChange = Math.round(dy / rowHeight);
            let newIndex = originalIndex + rowChange;
            
            // 确保不超出任务范围
            newIndex = Math.max(0, Math.min(tasks.length - 1, newIndex));
            
            if (newIndex !== originalIndex) {
                // 重新排序任务
                const movedTask = tasks[originalIndex];
                tasks.splice(originalIndex, 1);
                tasks.splice(newIndex, 0, movedTask);
                
                // 保存到本地存储
                localStorage.setItem('ganttTasks', JSON.stringify(tasks));
                
                // 重新渲染
                renderTasks();
                renderGanttChart();
            } else {
                // 恢复原来的位置
                currentRow.style.transform = 'translateY(0)';
            }
        });
        
        taskTableBody.appendChild(row);
    });

    // 添加2行空白行
    for (let i = 0; i < 2; i++) {
        const emptyRow = document.createElement('tr');
        emptyRow.style.height = `${FIXED_ROW_HEIGHT}px`; /* 使用统一的固定行高 */

        // 创建对应的空白单元格（包括甘特图单元格）
        const cells = ['', '', '', '', '', '', '', ''];
        cells.forEach((content, index) => {
            const cell = document.createElement('td');

            // 最后一个单元格是甘特图单元格
            if (index === cells.length - 1) {
                cell.className = 'gantt-cell';
                cell.style.minWidth = '800px';
            } else {
                cell.className = 'task-info-cell';
            }

            emptyRow.appendChild(cell);
        });

        taskTableBody.appendChild(emptyRow);
    }
    
    updateToolbarButtons();

    // 应用固定行高
    applyFixedRowHeight();
    
    // 重新应用列设置
    const savedColumns = JSON.parse(localStorage.getItem('ganttColumnSettings')) || {
        group: true,
        number: true,
        name: true,
        duration: true,
        start: true,
        end: true
    };
    applyColumnSettings(savedColumns);
    
    // 初始化列宽调整手柄
    addResizeHandles();
    loadColumnWidths();
    
    // 应用固定行高
    applyFixedRowHeight();
}

// 在表格单元格中创建任务条
function createTaskBarInCell(ganttCell, task, index) {
    const { start: projectStart, end: projectEnd } = getDateRange();
    const stepDays = getStepDays();

    // 计算单位宽度
    const timeUnits = getTimeUnits(projectStart, projectEnd, stepDays);
    let unitWidth = 40; // 基础单位宽度

    // 根据可视区域宽度动态调整单位宽度
    const viewportWidth = ganttCell.clientWidth || 800;
    const optimalUnitWidth = Math.min(40, Math.floor(viewportWidth / timeUnits.length));
    unitWidth = Math.max(20, optimalUnitWidth);

    // 计算任务的开始和结束位置
    const taskStart = new Date(task.start);
    const taskEnd = new Date(task.end);

    // 计算毫秒数
    const msPerUnit = 86400000 * stepDays; // 1天 = 86400000毫秒

    // 计算偏移量和宽度
    const startOffset = Math.max(0, (taskStart.getTime() - projectStart.getTime()) / msPerUnit);
    const endOffset = Math.max(startOffset, (taskEnd.getTime() - projectStart.getTime()) / msPerUnit);
    const displayDuration = Math.max(0.1, endOffset - startOffset);

    // 创建任务条
    const taskBar = document.createElement('div');
    taskBar.className = 'gantt-task-in-cell';
    taskBar.style.left = `${startOffset * unitWidth}px`;
    taskBar.style.width = `${displayDuration * unitWidth}px`;
    taskBar.style.backgroundColor = task.color;
    taskBar.textContent = task.name;
    taskBar.title = `${task.name} (${task.start} - ${task.end})`;
    taskBar.dataset.taskId = task.id;

    // 创建左侧调整手柄
    const leftHandle = document.createElement('div');
    leftHandle.className = 'task-resize-handle left-handle';
    leftHandle.style.left = '0px';
    leftHandle.style.cursor = 'ew-resize';

    // 创建右侧调整手柄
    const rightHandle = document.createElement('div');
    rightHandle.className = 'task-resize-handle right-handle';
    rightHandle.style.right = '0px';
    rightHandle.style.cursor = 'ew-resize';

    // 将调整手柄添加到任务条
    taskBar.appendChild(leftHandle);
    taskBar.appendChild(rightHandle);

    // 创建共享状态对象
    const sharedState = {
        isResizing: false,
        isMoving: false
    };

    // 添加拖拽调整功能
    addTaskResizeListeners(taskBar, leftHandle, rightHandle, task, ganttCell, unitWidth, stepDays, sharedState);

    // 添加整体移动功能
    addTaskMoveListeners(taskBar, leftHandle, rightHandle, task, ganttCell, unitWidth, stepDays, sharedState);

    // 添加点击事件
    taskBar.addEventListener('click', function(e) {
        // 如果点击的是调整手柄，不处理选中事件
        if (e.target.classList.contains('task-resize-handle')) {
            return;
        }
        e.stopPropagation();
        // 选中对应的任务行
        const row = taskBar.closest('tr');
        if (row) {
            document.querySelectorAll('#taskTableBody tr').forEach(r => r.classList.remove('selected'));
            row.classList.add('selected');
            selectedTaskId = task.id;
            updateToolbarButtons();
        }
    });

    ganttCell.appendChild(taskBar);
}

// 添加任务条拖拽调整功能
function addTaskResizeListeners(taskBar, leftHandle, rightHandle, task, ganttCell, unitWidth, stepDays, sharedState) {
    let resizeType = null; // 'left' 或 'right'
    let startX = 0;
    let originalLeft = 0;
    let originalWidth = 0;
    let originalStartDate = null;
    let originalEndDate = null;

    // 左侧手柄拖拽事件
    leftHandle.addEventListener('mousedown', function(e) {
        e.preventDefault();
        e.stopPropagation();
        startResize(e, 'left');
    });

    // 右侧手柄拖拽事件
    rightHandle.addEventListener('mousedown', function(e) {
        e.preventDefault();
        e.stopPropagation();
        startResize(e, 'right');
    });

    function startResize(e, type) {
        // 检查是否正在移动，如果是则不允许调整
        if (sharedState.isMoving) {
            return;
        }

        sharedState.isResizing = true;
        resizeType = type;
        startX = e.clientX;

        // 记录原始状态
        originalLeft = parseInt(taskBar.style.left) || 0;
        originalWidth = parseInt(taskBar.style.width) || 0;
        originalStartDate = new Date(task.start);
        originalEndDate = new Date(task.end);

        // 添加视觉反馈
        taskBar.classList.add('resizing');
        document.body.style.cursor = 'ew-resize';
        document.body.style.userSelect = 'none';

        // 添加全局事件监听器
        document.addEventListener('mousemove', handleResize);
        document.addEventListener('mouseup', endResize);
    }

    function handleResize(e) {
        if (!sharedState.isResizing) return;

        const deltaX = e.clientX - startX;
        const deltaUnits = Math.round(deltaX / unitWidth);

        if (resizeType === 'left') {
            // 调整开始时间（左侧手柄）
            const newLeft = Math.max(0, originalLeft + deltaX);
            const newWidth = Math.max(unitWidth, originalWidth - deltaX);

            taskBar.style.left = `${newLeft}px`;
            taskBar.style.width = `${newWidth}px`;

        } else if (resizeType === 'right') {
            // 调整结束时间（右侧手柄）
            const newWidth = Math.max(unitWidth, originalWidth + deltaX);
            taskBar.style.width = `${newWidth}px`;
        }
    }

    function endResize(e) {
        if (!sharedState.isResizing) return;

        const deltaX = e.clientX - startX;
        const deltaUnits = Math.round(deltaX / unitWidth);

        // 计算新的日期
        let newStartDate = new Date(originalStartDate);
        let newEndDate = new Date(originalEndDate);

        if (resizeType === 'left') {
            // 调整开始时间
            newStartDate.setDate(newStartDate.getDate() + (deltaUnits * stepDays));
            // 确保开始时间不晚于结束时间
            if (newStartDate >= newEndDate) {
                newStartDate = new Date(newEndDate);
                newStartDate.setDate(newStartDate.getDate() - stepDays);
            }
        } else if (resizeType === 'right') {
            // 调整结束时间
            newEndDate.setDate(newEndDate.getDate() + (deltaUnits * stepDays));
            // 确保结束时间不早于开始时间
            if (newEndDate <= newStartDate) {
                newEndDate = new Date(newStartDate);
                newEndDate.setDate(newEndDate.getDate() + stepDays);
            }
        }

        // 更新任务数据
        updateTaskDates(task.id, newStartDate, newEndDate);

        // 清理状态
        sharedState.isResizing = false;
        resizeType = null;
        taskBar.classList.remove('resizing');
        document.body.style.cursor = '';
        document.body.style.userSelect = '';

        // 移除全局事件监听器
        document.removeEventListener('mousemove', handleResize);
        document.removeEventListener('mouseup', endResize);

        // 重新渲染以确保数据同步
        renderTasks();
        renderGanttChart();

        // 应用固定行高
        setTimeout(() => {
            applyFixedRowHeight();
        }, 50);
    }
}

// 更新任务日期并同步到表格
function updateTaskDates(taskId, newStartDate, newEndDate) {
    // 查找并更新任务数据
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    if (taskIndex === -1) return;

    const task = tasks[taskIndex];

    // 格式化日期为YYYY-MM-DD格式
    const formatDate = (date) => {
        return date.toISOString().split('T')[0];
    };

    // 更新任务数据
    task.start = formatDate(newStartDate);
    task.end = formatDate(newEndDate);

    // 重新计算持续时间
    const timeDiff = newEndDate.getTime() - newStartDate.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 包含结束日期
    task.duration = daysDiff;

    // 保存到localStorage
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));

    // 同步更新表格中对应行的数据
    updateTaskRowData(taskId, task);

    console.log(`任务 "${task.name}" 日期已更新: ${task.start} - ${task.end} (${task.duration}天)`);
}

// 更新表格行中的任务数据显示
function updateTaskRowData(taskId, task) {
    const row = document.querySelector(`tr[data-id="${taskId}"]`);
    if (!row) return;

    // 更新持续时间列（第5列）
    const durationCell = row.children[4];
    if (durationCell) {
        durationCell.textContent = `${task.duration}天`;
        durationCell.setAttribute('data-content', `${task.duration}天`);
    }

    // 更新开始时间列（第6列）
    const startCell = row.children[5];
    if (startCell) {
        startCell.textContent = task.start;
        startCell.setAttribute('data-content', task.start);
    }

    // 更新结束时间列（第7列）
    const endCell = row.children[6];
    if (endCell) {
        endCell.textContent = task.end;
        endCell.setAttribute('data-content', task.end);
    }
}

// 添加任务条整体移动功能
function addTaskMoveListeners(taskBar, leftHandle, rightHandle, task, ganttCell, unitWidth, stepDays, sharedState) {
    let startX = 0;
    let originalLeft = 0;
    let originalStartDate = null;
    let originalEndDate = null;
    let originalDuration = 0;
    let hasMovedEnough = false; // 标记是否移动了足够的距离
    const MIN_MOVE_DISTANCE = 5; // 最小移动距离阈值（像素）

    // 创建中间拖拽区域（排除调整手柄）
    const moveArea = document.createElement('div');
    moveArea.className = 'task-move-area';
    moveArea.style.position = 'absolute';
    moveArea.style.left = '6px'; // 排除左侧手柄
    moveArea.style.right = '6px'; // 排除右侧手柄
    moveArea.style.top = '0';
    moveArea.style.bottom = '0';
    moveArea.style.cursor = 'move';
    moveArea.style.zIndex = '5'; // 低于调整手柄的z-index

    taskBar.appendChild(moveArea);

    // 鼠标悬停事件 - 显示移动光标
    taskBar.addEventListener('mouseenter', function(e) {
        if (!sharedState.isMoving && !sharedState.isResizing) {
            // 检查鼠标是否在中间区域
            const rect = taskBar.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const handleWidth = 6;

            if (mouseX > handleWidth && mouseX < rect.width - handleWidth) {
                taskBar.style.cursor = 'move';
            }
        }
    });

    taskBar.addEventListener('mouseleave', function() {
        if (!sharedState.isMoving && !sharedState.isResizing) {
            taskBar.style.cursor = 'pointer';
        }
    });

    // 鼠标移动事件 - 动态改变光标
    taskBar.addEventListener('mousemove', function(e) {
        if (!sharedState.isMoving && !sharedState.isResizing) {
            const rect = taskBar.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const handleWidth = 6;

            if (mouseX <= handleWidth) {
                taskBar.style.cursor = 'ew-resize'; // 左侧手柄区域
            } else if (mouseX >= rect.width - handleWidth) {
                taskBar.style.cursor = 'ew-resize'; // 右侧手柄区域
            } else {
                taskBar.style.cursor = 'move'; // 中间移动区域
            }
        }
    });

    // 中间区域拖拽事件
    taskBar.addEventListener('mousedown', function(e) {
        // 检查是否点击在调整手柄上
        if (e.target.classList.contains('task-resize-handle')) {
            return; // 让调整手柄处理
        }

        // 检查鼠标位置是否在中间区域
        const rect = taskBar.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const handleWidth = 6;

        if (mouseX > handleWidth && mouseX < rect.width - handleWidth) {
            e.preventDefault();
            e.stopPropagation();
            startMove(e);
        }
    });

    function startMove(e) {
        // 初始化移动状态，但不立即设置为移动中
        startX = e.clientX;
        hasMovedEnough = false;

        // 记录原始状态
        originalLeft = parseInt(taskBar.style.left) || 0;
        originalStartDate = new Date(task.start);
        originalEndDate = new Date(task.end);
        originalDuration = task.duration;

        // 添加全局事件监听器（但还不设置视觉反馈）
        document.addEventListener('mousemove', handleMove);
        document.addEventListener('mouseup', endMove);
    }

    function handleMove(e) {
        const deltaX = e.clientX - startX;
        const moveDistance = Math.abs(deltaX);

        // 检查是否移动了足够的距离
        if (!hasMovedEnough && moveDistance >= MIN_MOVE_DISTANCE) {
            hasMovedEnough = true;
            sharedState.isMoving = true;

            // 现在才添加视觉反馈
            taskBar.classList.add('moving');
            taskBar.style.cursor = 'grabbing';
            document.body.style.userSelect = 'none';
        }

        // 只有在确认移动后才更新位置
        if (hasMovedEnough && sharedState.isMoving) {
            const newLeft = Math.max(0, originalLeft + deltaX);
            taskBar.style.left = `${newLeft}px`;
        }
    }

    function endMove(e) {
        // 移除全局事件监听器
        document.removeEventListener('mousemove', handleMove);
        document.removeEventListener('mouseup', endMove);

        // 只有在真正移动了足够距离时才处理移动逻辑
        if (hasMovedEnough && sharedState.isMoving) {
            const deltaX = e.clientX - startX;
            const deltaUnits = Math.round(deltaX / unitWidth);

            // 只有当移动距离不为0时才更新数据
            if (deltaUnits !== 0) {
                // 计算新的日期
                let newStartDate = new Date(originalStartDate);
                let newEndDate = new Date(originalEndDate);

                // 根据步长调整日期
                const dayOffset = deltaUnits * stepDays;
                newStartDate.setDate(newStartDate.getDate() + dayOffset);
                newEndDate.setDate(newEndDate.getDate() + dayOffset);

                // 更新任务数据（保持持续时间不变）
                updateTaskDatesForMove(task.id, newStartDate, newEndDate, originalDuration);

                // 重新渲染以确保数据同步
                renderTasks();
                renderGanttChart();

                // 应用固定行高
                setTimeout(() => {
                    applyFixedRowHeight();
                }, 50);
            }
        }

        // 清理状态
        sharedState.isMoving = false;
        hasMovedEnough = false;
        taskBar.classList.remove('moving');
        taskBar.style.cursor = '';
        document.body.style.userSelect = '';
    }
}

// 更新任务日期（用于整体移动，保持持续时间不变）
function updateTaskDatesForMove(taskId, newStartDate, newEndDate, originalDuration) {
    // 查找并更新任务数据
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    if (taskIndex === -1) return;

    const task = tasks[taskIndex];

    // 格式化日期为YYYY-MM-DD格式
    const formatDate = (date) => {
        return date.toISOString().split('T')[0];
    };

    // 更新任务数据（保持持续时间不变）
    task.start = formatDate(newStartDate);
    task.end = formatDate(newEndDate);
    task.duration = originalDuration; // 保持原始持续时间

    // 保存到localStorage
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));

    // 同步更新表格中对应行的数据
    updateTaskRowData(taskId, task);

    console.log(`任务 "${task.name}" 已移动: ${task.start} - ${task.end} (持续时间保持${task.duration}天)`);
}

// 更新任务表格总宽度
function updateTaskTableWidth() {
    const headers = document.querySelectorAll('.task-header th');
    let totalWidth = 0;
    
    headers.forEach(header => {
        totalWidth += header.offsetWidth;
    });
    
    // 更新任务列表容器宽度
    const taskList = document.querySelector('.task-list');
    if (taskList) {
        taskList.style.width = totalWidth + 'px';
    }
    
    // 更新任务表头宽度
    const taskHeader = document.querySelector('.task-header');
    if (taskHeader) {
        taskHeader.style.width = totalWidth + 'px';
    }
}

// 更新工具栏按钮状态
function updateToolbarButtons() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const anySelected = selectedCheckboxes.length > 0;
    
    editTaskBtn.disabled = !selectedTaskId;
    deleteTaskBtn.disabled = !anySelected;
}

// 全选/取消全选
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.task-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        const row = checkbox.closest('tr');
        if (selectAll.checked) {
            row.classList.add('selected');
        } else {
            row.classList.remove('selected');
            selectedTaskId = null;
        }
    });
    updateToolbarButtons();
}

// 删除选中任务
function deleteSelectedTasks() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;
    
    if (confirm('确定要删除选中的任务吗？')) {
        selectedCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const taskId = row.dataset.id;
            tasks = tasks.filter(task => task.id !== taskId);
            row.remove();
        });
        
        // 保存到本地存储
        localStorage.setItem('ganttTasks', JSON.stringify(tasks));
        
        selectedTaskId = null;
        selectAll.checked = false;
        
        // 重新渲染
        renderTasks();
        renderGanttChart();
    }
}

// 打开分组管理模态框
function openGroupModal() {
    groupName.value = '';
    renderGroupList();
    groupModal.style.display = 'block';
}

// 渲染分组列表
function renderGroupList() {
    groupList.innerHTML = '';
    
    groups.forEach(group => {
        const li = document.createElement('li');
        li.innerHTML = `
            <span>${group}</span>
            <span class="delete-group" data-group="${group}">删除</span>
        `;
        
        const deleteBtn = li.querySelector('.delete-group');
        deleteBtn.addEventListener('click', function() {
            deleteGroup(group);
        });
        
        groupList.appendChild(li);
    });
}

// 添加新分组
function addNewGroup() {
    const name = groupName.value.trim();
    if (!name) return;
    
    if (!groups.includes(name)) {
        groups.push(name);
        localStorage.setItem('ganttGroups', JSON.stringify(groups));
        updateGroupOptions();
        renderGroupList();
        groupName.value = '';
    }
}

// 删除分组
function deleteGroup(groupName) {
    if (groups.length <= 1) {
        alert('至少需要保留一个分组');
        return;
    }
    
    if (confirm(`确定要删除分组 "${groupName}" 吗？`)) {
        groups = groups.filter(group => group !== groupName);
        localStorage.setItem('ganttGroups', JSON.stringify(groups));
        
        // 更新任务中的分组引用
        tasks.forEach(task => {
            if (task.group === groupName) {
                task.group = groups[0];
            }
        });
        
        localStorage.setItem('ganttTasks', JSON.stringify(tasks));
        
        updateGroupOptions();
        renderGroupList();
        renderTasks();
        renderGanttChart();
    }
}

// 批量上下移动任务
function moveSelectedTasksUp() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;
    
    const selectedIndices = Array.from(selectedCheckboxes).map(checkbox => {
        const row = checkbox.closest('tr');
        return parseInt(row.dataset.index);
    }).sort((a, b) => a - b);
    
    // 检查是否可以上移（不能移到顶部之外）
    if (selectedIndices[0] === 0) {
        alert('选中的任务已在最顶部，无法继续上移');
        return;
    }
    
    // 批量上移
    const newTasks = [...tasks];
    for (let i = 0; i < selectedIndices.length; i++) {
        const index = selectedIndices[i];
        const newIndex = index - 1;
        
        // 检查目标位置是否被其他选中任务占用
        if (!selectedIndices.includes(newIndex)) {
            [newTasks[index], newTasks[newIndex]] = [newTasks[newIndex], newTasks[index]];
            
            // 更新选中索引
            selectedIndices[i] = newIndex;
            for (let j = i + 1; j < selectedIndices.length; j++) {
                if (selectedIndices[j] === newIndex) {
                    selectedIndices[j] = index;
                }
            }
        }
    }
    
    tasks = newTasks;
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));
    renderTasks();
    renderGanttChart();
    
    // 重新选中之前选中的任务
    const newCheckboxes = document.querySelectorAll('.task-checkbox');
    newCheckboxes.forEach((checkbox, index) => {
        if (selectedIndices.includes(index)) {
            checkbox.checked = true;
            checkbox.closest('tr').classList.add('selected');
        }
    });
    updateToolbarButtons();
}

function moveSelectedTasksDown() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;
    
    const selectedIndices = Array.from(selectedCheckboxes).map(checkbox => {
        const row = checkbox.closest('tr');
        return parseInt(row.dataset.index);
    }).sort((a, b) => b - a);
    
    // 检查是否可以下移（不能移到底部之外）
    if (selectedIndices[0] === tasks.length - 1) {
        alert('选中的任务已在最底部，无法继续下移');
        return;
    }
    
    // 批量下移
    const newTasks = [...tasks];
    for (let i = 0; i < selectedIndices.length; i++) {
        const index = selectedIndices[i];
        const newIndex = index + 1;
        
        // 检查目标位置是否被其他选中任务占用
        if (!selectedIndices.includes(newIndex)) {
            [newTasks[index], newTasks[newIndex]] = [newTasks[newIndex], newTasks[index]];
            
            // 更新选中索引
            selectedIndices[i] = newIndex;
            for (let j = i + 1; j < selectedIndices.length; j++) {
                if (selectedIndices[j] === newIndex) {
                    selectedIndices[j] = index;
                }
            }
        }
    }
    
    tasks = newTasks;
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));
    renderTasks();
    renderGanttChart();
    
    // 重新选中之前选中的任务
    const newCheckboxes = document.querySelectorAll('.task-checkbox');
    newCheckboxes.forEach((checkbox, index) => {
        if (selectedIndices.includes(index)) {
            checkbox.checked = true;
            checkbox.closest('tr').classList.add('selected');
        }
    });
    updateToolbarButtons();
}

// 批量复制任务
function copySelectedTasks() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;
    
    const selectedTasks = Array.from(selectedCheckboxes).map(checkbox => {
        const row = checkbox.closest('tr');
        const taskId = row.dataset.id;
        return tasks.find(t => t.id === taskId);
    });
    
    // 创建新任务副本
    selectedTasks.forEach(originalTask => {
        const newTask = {
            id: 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            group: originalTask.group,
            name: originalTask.name + ' (复制)',
            duration: originalTask.duration,
            start: originalTask.start,
            end: originalTask.end,
            color: originalTask.color
        };
        tasks.push(newTask);
    });
    
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));
    renderTasks();
    renderGanttChart();
    
    // 保持原有选中状态不变，不自动清除
    updateToolbarButtons();
}

// 更新工具栏按钮状态
function updateToolbarButtons() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const anySelected = selectedCheckboxes.length > 0;
    
    editTaskBtn.disabled = !selectedTaskId;
    deleteTaskBtn.disabled = !anySelected;
    moveUpBtn.disabled = !anySelected;
    moveDownBtn.disabled = !anySelected;
    copyTaskBtn.disabled = !anySelected;
}

// 打开批量分组模态框
function openBatchGroupModal() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        alert('请先选择要分组的任务');
        return;
    }
    
    const groupSelect = document.getElementById('batchGroupSelect');
    groupSelect.innerHTML = '';
    
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        groupSelect.appendChild(option);
    });
    
    document.getElementById('batchGroupModal').style.display = 'block';
}

// 保存批量分组
function saveBatchGroup() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const newGroup = document.getElementById('batchGroupSelect').value;
    
    if (!newGroup) {
        alert('请选择分组');
        return;
    }
    
    selectedCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const taskId = row.dataset.id;
        const task = tasks.find(t => t.id === taskId);
        if (task) {
            task.group = newGroup;
        }
    });
    
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));
    renderTasks();
    renderGanttChart();
    closeBatchGroupModal();
}

// 关闭批量分组模态框
function closeBatchGroupModal() {
    document.getElementById('batchGroupModal').style.display = 'none';
}

// 打开批量颜色选择模态框
function openBatchColorModal() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    document.getElementById('batchColorModal').style.display = 'block';
}

// 应用批量颜色更改
function applyBatchColor() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const newColor = document.getElementById('batchColor').value;

    if (selectedCheckboxes.length === 0) {
        closeBatchColorModal();
        return;
    }

    // 更新选中任务的颜色
    selectedCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const taskId = row.dataset.id;
        const task = tasks.find(t => t.id === taskId);
        if (task) {
            task.color = newColor;
        }
    });

    // 保存到localStorage
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));

    // 重新渲染
    renderTasks();
    renderGanttChart();

    closeBatchColorModal();
}

// 关闭批量颜色选择模态框
function closeBatchColorModal() {
    document.getElementById('batchColorModal').style.display = 'none';
}

// 直接移动选中任务指定天数
function moveSelectedTasksByDays(direction) {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    // 从工具栏的移动天数输入框获取天数
    const moveDaysInput = document.getElementById('moveDaysInput');
    const moveDays = parseInt(moveDaysInput.value);

    if (!moveDays || moveDays < 1) {
        alert('请输入有效的移动天数（1-365天）');
        return;
    }

    // 记录选中的任务ID，用于重新渲染后恢复选中状态
    const selectedTaskIds = [];
    selectedCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const taskId = row.dataset.id;
        selectedTaskIds.push(taskId);
    });

    const moveOffset = direction === 'left' ? -moveDays : moveDays;

    // 更新选中任务的日期
    selectedCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const taskId = row.dataset.id;
        const task = tasks.find(t => t.id === taskId);
        if (task) {
            // 计算新的开始日期
            const startDate = new Date(task.start);
            startDate.setDate(startDate.getDate() + moveOffset);

            // 计算新的结束日期
            const endDate = new Date(task.end);
            endDate.setDate(endDate.getDate() + moveOffset);

            // 验证日期有效性
            if (startDate instanceof Date && !isNaN(startDate) &&
                endDate instanceof Date && !isNaN(endDate)) {
                task.start = startDate.toISOString().split('T')[0];
                task.end = endDate.toISOString().split('T')[0];
            }
        }
    });

    // 保存到localStorage
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));

    // 重新渲染
    renderTasks();
    renderGanttChart();

    // 重新选中之前选中的任务
    setTimeout(() => {
        const newCheckboxes = document.querySelectorAll('.task-checkbox');
        newCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const taskId = row.dataset.id;
            if (selectedTaskIds.includes(taskId)) {
                checkbox.checked = true;
                // 同时添加selected类到行，确保视觉状态一致
                row.classList.add('selected');
            }
        });

        // 更新工具栏按钮状态
        updateToolbarButtons();
    }, 0);

    // 显示操作结果
    const directionText = direction === 'left' ? '向左' : '向右';
    console.log(`已将${selectedCheckboxes.length}个任务${directionText}移动${moveDays}天`);
}

// 打开批量移动模态框
function openBatchMoveModal(direction) {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    const modal = document.getElementById('batchMoveModal');
    const title = document.getElementById('batchMoveTitle');

    title.textContent = direction === 'left' ? '批量向左移动任务' : '批量向右移动任务';
    modal.dataset.direction = direction;
    modal.style.display = 'block';
}

// 应用批量移动
function applyBatchMove() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const moveDays = parseInt(document.getElementById('moveDays').value);
    const direction = document.getElementById('batchMoveModal').dataset.direction;

    if (selectedCheckboxes.length === 0 || !moveDays || moveDays < 1) {
        closeBatchMoveModal();
        return;
    }

    const moveOffset = direction === 'left' ? -moveDays : moveDays;

    // 记录选中的任务ID，用于重新渲染后恢复选中状态
    const selectedTaskIds = [];
    selectedCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const taskId = row.dataset.id;
        selectedTaskIds.push(taskId);
    });

    // 更新选中任务的日期
    selectedCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const taskId = row.dataset.id;
        const task = tasks.find(t => t.id === taskId);
        if (task) {
            // 计算新的开始日期
            const startDate = new Date(task.start);
            startDate.setDate(startDate.getDate() + moveOffset);

            // 计算新的结束日期
            const endDate = new Date(task.end);
            endDate.setDate(endDate.getDate() + moveOffset);

            // 验证日期有效性
            if (startDate instanceof Date && !isNaN(startDate) &&
                endDate instanceof Date && !isNaN(endDate)) {
                task.start = startDate.toISOString().split('T')[0];
                task.end = endDate.toISOString().split('T')[0];
            }
        }
    });

    // 保存到localStorage
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));

    // 重新渲染
    renderTasks();
    renderGanttChart();

    // 重新选中之前选中的任务
    setTimeout(() => {
        const newCheckboxes = document.querySelectorAll('.task-checkbox');
        newCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const taskId = row.dataset.id;
            if (selectedTaskIds.includes(taskId)) {
                checkbox.checked = true;
                // 同时添加selected类到行，确保视觉状态一致
                row.classList.add('selected');
            }
        });

        // 更新工具栏按钮状态
        updateToolbarButtons();
    }, 0);

    closeBatchMoveModal();
}

// 关闭批量移动模态框
function closeBatchMoveModal() {
    document.getElementById('batchMoveModal').style.display = 'none';
}

// 调整甘特图主体高度，确保任务条不被横向滚动条遮挡
function adjustGanttBodyHeight() {
    const unifiedBody = document.querySelector('.unified-body');
    const ganttChart = document.getElementById('ganttChart');
    const taskCount = tasks.length;
    
    if (!unifiedBody || !ganttChart) return;
    
    // 计算需要的高度：任务条高度 + 额外空间
    const taskHeight = FIXED_ROW_HEIGHT; // 每个任务行高度
    const minHeight = 200; // 最小高度
    const calculatedHeight = Math.max(minHeight, taskCount * taskHeight);
    
    // 设置甘特图容器的高度，与任务列表同步
    ganttChart.style.minHeight = `${calculatedHeight}px`;
    ganttChart.style.height = `${calculatedHeight}px`;
    
    console.log(`调整甘特图高度: ${calculatedHeight}px, 任务数: ${taskCount}`);
}

// 修复垂直滚动偏差问题
function fixScrollAlignment() {
    const taskTableBody = document.getElementById('taskTableBody');
    const ganttChart = document.getElementById('ganttChart');
    
    if (!taskTableBody || !ganttChart) return;
    
    // 强制同步高度
    const taskRows = taskTableBody.querySelectorAll('tr');
    const ganttTasks = ganttChart.querySelectorAll('.gantt-task-container');
    
    if (taskRows.length !== ganttTasks.length) {
        console.warn('任务行数与任务条数不匹配');
    }
    
    // 确保每个任务行和任务条高度一致
    taskRows.forEach((row, index) => {
        const taskBar = ganttTasks[index];
        if (taskBar) {
            // 强制设置相同的高度和位置
            row.style.height = `${FIXED_ROW_HEIGHT}px`;
            taskBar.style.top = `${index * FIXED_ROW_HEIGHT}px`;
            taskBar.style.height = `${FIXED_ROW_HEIGHT}px`;
        }
    });
    
    // 设置统一容器高度
    const totalHeight = tasks.length * 50 + 100; // 额外添加空白空间
    ganttChart.style.minHeight = `${totalHeight}px`;
    ganttChart.style.height = `${totalHeight}px`;
    
    console.log(`修复滚动对齐: ${tasks.length}个任务, 总高度: ${totalHeight}px`);
}

// 更新工具栏按钮状态
function updateToolbarButtons() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const anySelected = selectedCheckboxes.length > 0;

    editTaskBtn.disabled = !selectedTaskId;
    deleteTaskBtn.disabled = !anySelected;
    moveUpBtn.disabled = !anySelected;
    moveDownBtn.disabled = !anySelected;
    copyTaskBtn.disabled = !anySelected;
    batchColorBtn.disabled = !anySelected;
    moveLeftBtn.disabled = !anySelected;
    moveRightBtn.disabled = !anySelected;
    document.getElementById('batchGroupBtn').disabled = !anySelected;
}

// 关闭所有模态框
function closeAllModals() {
    taskModal.style.display = 'none';
    groupModal.style.display = 'none';
    document.getElementById('columnModal').style.display = 'none';
    document.getElementById('batchGroupModal').style.display = 'none';
    document.getElementById('borderModal').style.display = 'none';
    document.getElementById('batchColorModal').style.display = 'none';
    document.getElementById('batchMoveModal').style.display = 'none';
}

// 获取日期范围（自动扩展版本）
function getDateRange() {
    if (tasks.length === 0) {
        const today = new Date();
        const endDate = new Date();
        endDate.setMonth(today.getMonth() + 3);
        return { start: today, end: endDate };
    }

    let startDate = new Date(tasks[0].start);
    let endDate = new Date(tasks[0].end);

    tasks.forEach(task => {
        const taskStart = new Date(task.start);
        const taskEnd = new Date(task.end);

        if (taskStart < startDate) startDate = taskStart;
        if (taskEnd > endDate) endDate = taskEnd;
    });

    // 添加前后缓冲期，确保有空间操作
    // 减少前缓冲以避免任务条前面的空白区域，但保留少量空间用于操作
    const bufferStart = new Date(startDate);
    const bufferEnd = new Date(endDate);
    bufferStart.setDate(startDate.getDate() - 1); // 提前1天（减少空白区域）
    bufferEnd.setDate(endDate.getDate() + 7); // 延后7天（保持足够的后续空间）

    return { start: bufferStart, end: bufferEnd };
}

// 渲染甘特图
function renderGanttChart() {
    const { start, end } = getDateRange();
    const stepDays = getStepDays();

    // 计算单位宽度
    const timeUnits = getTimeUnits(start, end, stepDays);
    let unitWidth = 40; // 基础单位宽度

    // 根据可视区域宽度和时间单位数量动态调整单位宽度
    const viewportWidth = document.querySelector('.gantt-chart').clientWidth || 800;
    const optimalUnitWidth = Math.min(40, Math.floor(viewportWidth / timeUnits.length));
    unitWidth = Math.max(20, optimalUnitWidth); // 设置最小单位宽度

    // 更新CSS变量以同步任务条和时间标尺的单位宽度
    document.documentElement.style.setProperty('--unit-width', `${unitWidth}px`);
    
    // 更新甘特图背景虚线
    const ganttChart = document.getElementById('ganttChart');
    if (ganttChart) {
        ganttChart.style.backgroundImage = `repeating-linear-gradient(
            90deg,
            transparent,
            transparent ${unitWidth - 1}px,
            #ddd ${unitWidth - 1}px,
            #ddd ${unitWidth}px
        )`;
    }

    // 计算总宽度
    const totalWidth = timeUnits.length * unitWidth;
    
    // 设置时间标尺和甘特图的宽度一致
    const timeRuler = document.getElementById('timeRuler');
    if (timeRuler) {
        timeRuler.style.width = `${totalWidth}px`;
    }
    
    renderTimeRuler(start, end, stepDays, unitWidth);
    renderTaskBars(start, end, stepDays, unitWidth);

    // 应用固定行高（替换所有旧的高度函数）
    applyFixedRowHeight();

    // 初始化列宽调整手柄
    addResizeHandles();
}

// 更新任务表格总宽度
function updateTaskTableWidth() {
    const headers = document.querySelectorAll('.task-header th');
    let totalWidth = 0;
    
    headers.forEach(header => {
        totalWidth += header.offsetWidth;
    });
    
    // 更新任务列表容器宽度
    const taskList = document.querySelector('.task-list');
    if (taskList) {
        taskList.style.width = totalWidth + 'px';
    }
    
    // 更新任务表头宽度
    const taskHeader = document.querySelector('.task-header');
    if (taskHeader) {
        taskHeader.style.width = totalWidth + 'px';
    }
}

// 渲染时间标尺
function renderTimeRuler(startDate, endDate, stepDays, unitWidth) {
    // 清空第一行的时间标尺（月份行）
    const firstRowTimeRuler = document.getElementById('timeRuler');
    if (firstRowTimeRuler) {
        firstRowTimeRuler.innerHTML = '';
    }

    // 清空第二行的时间标尺（日期行）
    const secondRowTimeRuler = document.querySelector('thead tr:nth-child(2) th');
    if (secondRowTimeRuler) {
        secondRowTimeRuler.innerHTML = '';
    }

    // 获取时间单位
    const timeUnits = getTimeUnits(startDate, endDate, stepDays);

    // 创建月份行
    const monthRow = document.createElement('div');
    monthRow.className = 'month-row';
    monthRow.style.position = 'absolute';
    monthRow.style.top = '0';
    monthRow.style.left = '0';
    monthRow.style.height = `${FIXED_ROW_HEIGHT}px`; /* 使用统一的固定行高 */
    monthRow.style.width = '100%';

    // 创建日期行
    const dateRow = document.createElement('div');
    dateRow.className = 'date-row';
    dateRow.style.position = 'absolute';
    dateRow.style.top = '0';
    dateRow.style.left = '0';
    dateRow.style.height = `${FIXED_ROW_HEIGHT}px`; /* 使用统一的固定行高 */
    dateRow.style.width = '100%';

    // 计算精确的月份边界对齐
    const monthBoundaries = [];
    let current = new Date(startDate);
    current.setHours(0, 0, 0, 0);

    // 计算每个月的精确宽度（以时间单位为单位）
    const monthWidths = new Map();

    // 遍历所有时间单位，计算每个月包含的时间单位数量
    timeUnits.forEach((date, index) => {
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

        if (!monthWidths.has(monthKey)) {
            monthWidths.set(monthKey, 0);
        }
        monthWidths.set(monthKey, monthWidths.get(monthKey) + 1);
    });

    // 渲染月份（精确对齐到时间单位边界）
    monthWidths.forEach((unitCount, monthKey) => {
        const [year, month] = monthKey.split('-');
        const monthDiv = document.createElement('div');
        monthDiv.className = 'month-unit';
        monthDiv.style.width = `${unitCount * unitWidth}px`;
        monthDiv.textContent = `${year}/${month}`;
        monthRow.appendChild(monthDiv);
    });

    // 渲染日期（与月份保持精确对齐）
    timeUnits.forEach(date => {
        const dateDiv = document.createElement('div');
        dateDiv.className = 'date-unit';
        dateDiv.style.width = `${unitWidth}px`;
        dateDiv.textContent = formatDate(date, stepDays);
        dateDiv.style.backgroundColor = '#fff';
        dateDiv.style.color = '#333';
        dateDiv.style.border = '1px solid #ddd';
        dateRow.appendChild(dateDiv);
    });

    console.log(`时间单位数量: ${timeUnits.length}, 步长天数: ${stepDays}`);

    // 将月份行添加到第一行的甘特图单元格
    if (firstRowTimeRuler) {
        firstRowTimeRuler.appendChild(monthRow);
        firstRowTimeRuler.style.height = `${FIXED_ROW_HEIGHT}px`; /* 使用统一的固定行高 */
        firstRowTimeRuler.style.position = 'relative';
    }

    // 将日期行添加到第二行的甘特图单元格
    if (secondRowTimeRuler) {
        secondRowTimeRuler.appendChild(dateRow);
        secondRowTimeRuler.style.height = `${FIXED_ROW_HEIGHT}px`; /* 使用统一的固定行高 */
        secondRowTimeRuler.style.position = 'relative';
    }
}

// 获取时间单位
function getTimeUnits(startDate, endDate, stepDays) {
    const units = [];
    const current = new Date(startDate);
    current.setHours(0, 0, 0, 0); // 确保时间从当天开始
    
    // 确保endDate的时间也设置为当天结束，避免时间比较误差
    const normalizedEndDate = new Date(endDate);
    normalizedEndDate.setHours(23, 59, 59, 999);
    
    while (current <= normalizedEndDate) {
        units.push(new Date(current));
        current.setDate(current.getDate() + stepDays);
        current.setHours(0, 0, 0, 0); // 重置为当天开始
    }
    
    return units;
}

// 获取月份边界
function getMonthBoundaries(startDate, endDate) {
    const boundaries = [];
    let current = new Date(startDate);
    current.setHours(0, 0, 0, 0);
    
    // 找到第一个月的开始
    current.setDate(1);
    boundaries.push(new Date(current));
    
    // 添加后续月份的开始
    const normalizedEndDate = new Date(endDate);
    normalizedEndDate.setHours(23, 59, 59, 999);
    
    while (current < normalizedEndDate) {
        current.setMonth(current.getMonth() + 1);
        current.setDate(1);
        boundaries.push(new Date(current));
    }
    
    return boundaries;
}

// 获取月份天数
function getDaysInMonth(year, month) {
    return new Date(year, month + 1, 0).getDate();
}

// 格式化日期 - 显示n,2n...xn或真实的最后一天
function formatDate(date, stepDays) {
    const year = date.getFullYear();
    const month = date.getMonth();
    const currentDay = date.getDate();

    // 获取当前月的天数
    const daysInMonth = getDaysInMonth(year, month);

    // 使用传入的步长间隔
    const interval = stepDays;
    
    // 计算当前日期在所有时间单位中的位置
    const startOfMonth = new Date(year, month, 1);
    const daysFromStart = currentDay;
    
    // 计算序列位置
    const sequenceIndex = Math.floor((daysFromStart - 1) / interval);
    let displayValue = (sequenceIndex + 1) * interval;
    
    // 检查当前日期是否是该月的最后一个时间单位
    const nextTimeUnit = new Date(date);
    nextTimeUnit.setDate(date.getDate() + interval);
    const isLastInMonth = nextTimeUnit.getMonth() !== month || nextTimeUnit.getDate() > daysInMonth;
    
    // 如果当前是最后一个时间单位，显示真实的最后一天
    if (isLastInMonth) {
        return `${daysInMonth}`;
    }
    
    // 否则显示正常的倍数序列
    return `${displayValue}`;
}

// 渲染任务条
function renderTaskBars(startDate, endDate, stepDays, unitWidth) {
    ganttChart.innerHTML = '';

    // 计算时间单位
    const timeUnits = getTimeUnits(startDate, endDate, stepDays);
    const totalWidth = timeUnits.length * unitWidth;

    // 设置甘特图图表宽度与时间标尺一致
    ganttChart.style.width = `${totalWidth}px`;
    ganttChart.style.minWidth = '100%';

    // 计算毫秒数
    const msPerUnit = 86400000 * stepDays; // 1天 = 86400000毫秒
    
    tasks.forEach((task, index) => {
        const taskStart = new Date(task.start);
        const taskEnd = new Date(task.end);
        
        // 确保时间从0点开始计算，避免时间偏移
        taskStart.setHours(0, 0, 0, 0);
        startDate.setHours(0, 0, 0, 0);
        
        // 计算位置
        const timeDiff = taskStart - startDate;
        const startOffset = Math.max(0, Math.round(timeDiff / msPerUnit));
        
        // 使用存储的duration值，不再重新计算
        const durationMs = taskEnd - taskStart;
        const durationUnits = Math.max(1, Math.round(durationMs / msPerUnit));
        
        // 确保持续时间至少为1个单位
        const displayDuration = Math.max(1, durationUnits);
        
        const taskBarContainer = document.createElement('div');
        taskBarContainer.className = 'gantt-task-container';

        // 使用新的固定行高计算
        const { rowHeight, taskBarHeight } = calculateFixedRowHeight();
        taskBarContainer.style.top = `${index * rowHeight}px`; // 使用计算的行高
        taskBarContainer.style.height = `${rowHeight}px`; // 使用计算的行高
        
        const taskBar = document.createElement('div');
        taskBar.className = 'gantt-task';
        taskBar.style.left = `${startOffset * unitWidth}px`;
        taskBar.style.width = `${displayDuration * unitWidth}px`;
        taskBar.style.backgroundColor = task.color; // 设置任务条颜色
        
        const taskLabel = document.createElement('div');
        taskLabel.className = 'gantt-task-label';
        taskLabel.textContent = task.name;
            // 确保文字始终在任务条右侧，添加5px间距
            taskLabel.style.left = `${(startOffset + displayDuration) * unitWidth + 5}px`;
            taskLabel.style.fontSize = '16px';
            taskLabel.style.fontWeight = 'bold';
            taskLabel.style.color = '#333';
            taskLabel.style.lineHeight = `${rowHeight}px`; // 使用计算的行高
        
        // 将任务条和标签添加到容器
        taskBarContainer.appendChild(taskBar);
        taskBarContainer.appendChild(taskLabel);
        
        // 添加上下拖拽功能 - 使用单次点击而非双击
        let isVerticalDragging = false;
        let startY;
        let originalTop;
        let originalIndex = index;

        // 单击任务条启用垂直拖拽
        taskBarContainer.addEventListener('mousedown', function(e) {
            if (e.button === 0 && !isMoving && !isResizing) { // 左键且不在其他操作中
                isVerticalDragging = true;
                startY = e.clientY;
                originalTop = parseInt(taskBarContainer.style.top);
                taskBarContainer.style.cursor = 'ns-resize';
                document.body.style.userSelect = 'none';
                e.preventDefault();
                e.stopPropagation();
            }
        });

        document.addEventListener('mousemove', function(e) {
            if (!isVerticalDragging) return;

            const dy = e.clientY - startY;
            const rowChange = Math.round(dy / 50);
            let newIndex = originalIndex + rowChange;

            // 确保任务条不超出任务范围
            newIndex = Math.max(0, Math.min(tasks.length - 1, newIndex));

            // 更新任务条位置
            taskBarContainer.style.top = `${newIndex * 50}px`;
        });

        document.addEventListener('mouseup', function(e) {
            if (!isVerticalDragging) return;

            isVerticalDragging = false;
            taskBarContainer.style.cursor = 'grab';
            document.body.style.userSelect = '';

            // 计算新的索引
            const newIndex = Math.round(parseInt(taskBarContainer.style.top) / 50);

            if (newIndex !== originalIndex && newIndex >= 0 && newIndex < tasks.length) {
                // 重新排序任务
                const movedTask = tasks[originalIndex];
                tasks.splice(originalIndex, 1);
                tasks.splice(newIndex, 0, movedTask);

                // 保存到本地存储
                localStorage.setItem('ganttTasks', JSON.stringify(tasks));

                // 重新渲染
                renderTasks();
                renderGanttChart();
            } else {
                // 恢复原来的位置
                taskBarContainer.style.top = `${originalIndex * 50}px`;
            }
        });
        
        // 添加拖拽功能 - 左右调整时间（移动整个任务条）
        let isMoving = false;
        let startX;
        let originalLeft;
        let originalStartOffset = startOffset;

        taskBar.addEventListener('mousedown', function(e) {
            if (e.button === 0 && !isResizing && !isVerticalDragging) { // 左键且不在其他操作中
                isMoving = true;
                startX = e.clientX;
                originalLeft = parseInt(taskBar.style.left);
                originalStartOffset = startOffset;
                // 重置可能的残留状态
                isResizing = false;
                taskBar.style.cursor = 'grabbing';
                document.body.style.userSelect = 'none';
                e.preventDefault();
                e.stopPropagation();
            }
        });

        document.addEventListener('mousemove', function(e) {
            if (!isMoving) return;

            const dx = e.clientX - startX;
            const unitChange = Math.round(dx / unitWidth);
            let newStartOffset = originalStartOffset + unitChange;

            // 确保任务条不超出甘特图边界
            newStartOffset = Math.max(0, newStartOffset);

            // 更新任务条和标签位置
            taskBar.style.left = `${newStartOffset * unitWidth}px`;
            taskLabel.style.left = `${(newStartOffset + displayDuration) * unitWidth + 5}px`;
        });

        document.addEventListener('mouseup', function() {
            if (!isMoving) return;

            isMoving = false;
            taskBar.style.cursor = 'grab';
            document.body.style.userSelect = '';

            // 计算新的开始和结束日期
            const newStartOffset = parseInt(taskBar.style.left) / unitWidth;
            const newStartDate = new Date(startDate);
            newStartDate.setDate(startDate.getDate() + newStartOffset * stepDays);
            const newEndDate = new Date(newStartDate);
            newEndDate.setDate(newStartDate.getDate() + displayDuration * stepDays - 1);

            // 更新任务数据
            const taskIndex = tasks.findIndex(t => t.id === task.id);
            if (taskIndex !== -1) {
                tasks[taskIndex].start = newStartDate.toISOString().split('T')[0];
                tasks[taskIndex].end = newEndDate.toISOString().split('T')[0];
                // 持续时间不变

                // 保存到本地存储
                localStorage.setItem('ganttTasks', JSON.stringify(tasks));

                // 重新渲染任务表格和甘特图（自动调整日期范围）
                renderTasks();
                renderGanttChart();
            }
        });

        // 添加拖拽功能 - 调整任务条长度
        let isResizing = false;
        let resizeStartX;
        let originalDurationWidth;

        // 创建调整手柄
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'gantt-task-resize-handle';
        taskBar.appendChild(resizeHandle);

        resizeHandle.addEventListener('mousedown', function(e) {
            e.stopPropagation(); // 防止触发移动事件
            isResizing = true;
            resizeStartX = e.clientX;
            originalDurationWidth = parseInt(taskBar.style.width);
            document.body.style.cursor = 'ew-resize';
            document.body.style.userSelect = 'none';
            e.preventDefault();
        });

        document.addEventListener('mousemove', function(e) {
            if (!isResizing) return;

            const dx = e.clientX - resizeStartX;
            const unitChange = Math.round(dx / unitWidth);
            let newDurationWidth = originalDurationWidth + unitChange * unitWidth;

            // 确保持续时间至少为1个单位
            newDurationWidth = Math.max(unitWidth, newDurationWidth);

            // 更新任务条宽度和标签位置
            taskBar.style.width = `${newDurationWidth}px`;
            const newDisplayDuration = Math.round(newDurationWidth / unitWidth);
            taskLabel.style.left = `${(startOffset + newDisplayDuration) * unitWidth + 5}px`;
        });

        document.addEventListener('mouseup', function() {
            if (!isResizing) return;

            isResizing = false;
            document.body.style.cursor = 'default';
            document.body.style.userSelect = '';
            // 重置移动状态
            isMoving = false;

            // 计算新的持续时间
            const newDurationWidth = parseInt(taskBar.style.width);
            const newDisplayDuration = Math.round(newDurationWidth / unitWidth);
            const newDurationDays = newDisplayDuration * stepDays;

            // 计算新的结束日期
            const taskIndex = tasks.findIndex(t => t.id === task.id);
            if (taskIndex !== -1) {
                const taskStartDate = new Date(tasks[taskIndex].start);
                const newEndDate = new Date(taskStartDate);
                newEndDate.setDate(taskStartDate.getDate() + newDurationDays - 1);

                tasks[taskIndex].end = newEndDate.toISOString().split('T')[0];
                tasks[taskIndex].duration = newDurationDays;

                // 保存到本地存储
                localStorage.setItem('ganttTasks', JSON.stringify(tasks));

                // 重新渲染任务表格和甘特图（自动调整日期范围）
                renderTasks();
                renderGanttChart();
            }
        });

        // 添加点击事件
        taskBarContainer.addEventListener('click', function(e) {
            if (!isDragging) { // 只有在非拖拽状态下才处理点击
                // 选中对应的任务行
                const row = document.querySelector(`tr[data-id="${task.id}"]`);
                if (row) {
                    document.querySelectorAll('#taskTableBody tr').forEach(r => r.classList.remove('selected'));
                    row.classList.add('selected');
                    selectedTaskId = task.id;
                    updateToolbarButtons();
                }
            }
        });

        // 添加拖拽事件监听器
        addDragListeners(taskBar, task, index);
        
        ganttChart.appendChild(taskBarContainer);
    });
    
    // 添加2行空白任务条区域
    for (let i = 0; i < 2; i++) {
        const emptyContainer = document.createElement('div');
        emptyContainer.className = 'gantt-task-container';
        emptyContainer.style.top = `${(tasks.length + i) * FIXED_ROW_HEIGHT}px`;
        emptyContainer.style.height = `${FIXED_ROW_HEIGHT}px`;
        ganttChart.appendChild(emptyContainer);
    }
}

// 页面加载时添加示例数据
function addSampleData() {
    if (tasks.length === 0) {
        // 获取当前日期作为项目开始日期
        const today = new Date();
        const startDate = today.toISOString().split('T')[0];

        // 计算各任务的开始和结束日期
        const task1Start = new Date(today);
        const task1End = new Date(task1Start);
        task1End.setDate(task1Start.getDate() + 4); // 5天任务

        const task2Start = new Date(task1End);
        task2Start.setDate(task1End.getDate() + 1);
        const task2End = new Date(task2Start);
        task2End.setDate(task2Start.getDate() + 9); // 10天任务

        const task3Start = new Date(task1Start);
        task3Start.setDate(task1Start.getDate() + 4); // 与任务1重叠
        const task3End = new Date(task3Start);
        task3End.setDate(task3Start.getDate() + 6); // 7天任务

        const task4Start = new Date(task3End);
        task4Start.setDate(task3End.getDate() + 1);
        const task4End = new Date(task4Start);
        task4End.setDate(task4Start.getDate() + 19); // 20天任务

        const task5Start = new Date(task4End);
        task5Start.setDate(task4End.getDate() + 1);
        const task5End = new Date(task5Start);
        task5End.setDate(task5Start.getDate() + 4); // 5天任务

        const sampleTasks = [
            {
                id: 'task_1',
                group: '前期',
                name: '项目启动',
                duration: 5,
                start: task1Start.toISOString().split('T')[0],
                end: task1End.toISOString().split('T')[0],
                color: '#e74c3c'
            },
            {
                id: 'task_2',
                group: '前期',
                name: '需求分析',
                duration: 10,
                start: task2Start.toISOString().split('T')[0],
                end: task2End.toISOString().split('T')[0],
                color: '#3498db'
            },
            {
                id: 'task_3',
                group: '幕墙施工',
                name: '材料采购',
                duration: 7,
                start: task3Start.toISOString().split('T')[0],
                end: task3End.toISOString().split('T')[0],
                color: '#2ecc71'
            },
            {
                id: 'task_4',
                group: '幕墙施工',
                name: '安装施工',
                duration: 20,
                start: task4Start.toISOString().split('T')[0],
                end: task4End.toISOString().split('T')[0],
                color: '#f39c12'
            },
            {
                id: 'task_5',
                group: '后期',
                name: '验收测试',
                duration: 5,
                start: task5Start.toISOString().split('T')[0],
                end: task5End.toISOString().split('T')[0],
                color: '#9b59b6'
            }
        ];
        
        tasks = sampleTasks;
        localStorage.setItem('ganttTasks', JSON.stringify(tasks));
        renderTasks();
        renderGanttChart();
    }
}

// 添加示例数据
// 更新总工期显示
function updateDateRangeDisplay() {
    const startDateDisplay = document.getElementById('startDateDisplay');
    const endDateDisplay = document.getElementById('endDateDisplay');
    
    if (tasks.length === 0) {
        startDateDisplay.textContent = '--';
        endDateDisplay.textContent = '--';
        return;
    }
    
    let minDate = new Date(tasks[0].start);
    let maxDate = new Date(tasks[0].end);
    
    tasks.forEach(task => {
        const taskStart = new Date(task.start);
        const taskEnd = new Date(task.end);
        
        if (taskStart < minDate) minDate = taskStart;
        if (taskEnd > maxDate) maxDate = taskEnd;
    });
    
    // 格式化日期显示
    const formatDate = (date) => {
        return date.toISOString().split('T')[0];
    };
    
    startDateDisplay.textContent = formatDate(minDate);
    endDateDisplay.textContent = formatDate(maxDate);
}

// 在每次渲染任务后更新日期范围显示和应用边框样式
const originalRenderTasks = renderTasks;
renderTasks = function() {
    originalRenderTasks();
    updateDateRangeDisplay();
    // 触发任务渲染完成事件
    document.dispatchEvent(new CustomEvent('tasksRendered'));
};

// 在每次渲染甘特图后更新日期范围显示和应用边框样式
const originalRenderGanttChart = renderGanttChart;
renderGanttChart = function() {
    originalRenderGanttChart();
    updateDateRangeDisplay();
    // 触发甘特图渲染完成事件
    document.dispatchEvent(new CustomEvent('ganttRendered'));
};

addSampleData();
updateDateRangeDisplay();

// Excel导入导出功能
function exportToExcel() {
    if (tasks.length === 0) {
        alert('没有可导出的任务数据');
        return;
    }

    try {
        // 按照网页显示顺序准备导出数据
        const exportData = tasks.map((task, index) => ({
            '分组名': task.group,
            '编号': String(index + 1).padStart(3, '0'),
            '工作名称': task.name,
            '持续时间': task.duration,
            '开始时间': task.start,
            '结束时间': task.end
        }));

        // 创建工作表
        const ws = XLSX.utils.json_to_sheet(exportData);
        
        // 设置列宽（与网页显示顺序一致）
        const colWidths = [
            { wch: 15 }, // 分组名
            { wch: 8 },  // 编号
            { wch: 30 }, // 工作名称
            { wch: 12 }, // 持续时间
            { wch: 15 }, // 开始时间
            { wch: 15 }  // 结束时间
        ];
        ws['!cols'] = colWidths;

        // 创建工作簿
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "施工进度");

        // 导出文件
        const fileName = `施工进度甘特图_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);

    } catch (error) {
        console.error('导出失败:', error);
        alert('导出失败，请重试');
    }
}

function importFromExcel(file) {
    if (!file) {
        alert('请选择Excel文件');
        return;
    }

    try {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                
                // 获取第一个工作表
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                
                // 转换为JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet);
                
                if (jsonData.length === 0) {
                    alert('Excel文件中没有数据');
                    return;
                }

                // 验证数据格式（与导出格式一致）
                const requiredFields = ['分组名', '工作名称', '持续时间', '开始时间', '结束时间'];
                const hasValidData = jsonData.every(row =>
                    row && requiredFields.every(field => row[field] !== undefined && row[field] !== null && row[field] !== '')
                );

                if (!hasValidData) {
                    // 检查缺失的字段
                    const missingFields = [];
                    const sampleRow = jsonData[0] || {};
                    requiredFields.forEach(field => {
                        if (sampleRow[field] === undefined || sampleRow[field] === null || sampleRow[field] === '') {
                            missingFields.push(field);
                        }
                    });

                    alert(`Excel文件格式不正确，缺少必需的列：${missingFields.join('、')}\n请确保包含：分组名、编号、工作名称、持续时间、开始时间、结束时间列`);
                    return;
                }

                // 转换数据格式（与导出格式一致）
                const importedTasks = jsonData.filter(row => row && row['工作名称']).map((row, index) => {
                    try {
                        // 解析持续时间
                        let duration = parseInt(row['持续时间']);
                        if (isNaN(duration) || duration < 1) {
                            duration = 1;
                        }

                        // 解析日期
                        const startDate = formatExcelDate(row['开始时间']);
                        const endDate = formatExcelDate(row['结束时间']);

                        const task = {
                            id: 'task_' + Date.now() + '_' + index,
                            group: String(row['分组名'] || '默认分组').trim(),
                            name: String(row['工作名称'] || '').trim(),
                            duration: duration,
                            start: startDate,
                            end: endDate,
                            color: '#e74c3c'
                        };

                        // 验证任务名称不为空
                        if (!task.name) {
                            throw new Error(`第${index + 1}行：工作名称不能为空`);
                        }

                        return task;
                    } catch (error) {
                        console.warn(`导入第${index + 1}行数据时出错:`, error, row);
                        throw error;
                    }
                });

                // 确保所有分组存在
                importedTasks.forEach(task => {
                    if (task.group && !groups.includes(task.group)) {
                        groups.push(task.group);
                    }
                });

                // 更新数据
                tasks = importedTasks;
                localStorage.setItem('ganttTasks', JSON.stringify(tasks));
                localStorage.setItem('ganttGroups', JSON.stringify(groups));

                // 更新界面
                updateGroupOptions();
                renderTasks();
                renderGanttChart();

                alert(`成功导入 ${importedTasks.length} 条任务`);

            } catch (error) {
                console.error('导入失败:', error);
                alert('导入失败：' + error.message);
            }
        };
        
        reader.readAsArrayBuffer(file);
        
    } catch (error) {
        console.error('文件读取失败:', error);
        alert('文件读取失败，请重试');
    }
}

// 处理Excel日期格式
function formatExcelDate(dateValue) {
    if (!dateValue) return new Date().toISOString().split('T')[0];

    try {
        // 如果是数字日期（Excel序列号）
        if (typeof dateValue === 'number') {
            // Excel日期系统：1900年1月1日是序列号1
            // 但Excel错误地将1900年当作闰年，所以需要修正
            let days = dateValue;

            // 如果日期大于59（1900年2月28日），需要减去1天来修正Excel的闰年bug
            if (days > 59) {
                days = days - 1;
            }

            // Excel的起始日期是1900年1月1日
            const excelEpoch = new Date(1900, 0, 1);
            const date = new Date(excelEpoch.getTime() + (days - 1) * 24 * 60 * 60 * 1000);
            return date.toISOString().split('T')[0];
        }

        // 如果是字符串日期
        if (typeof dateValue === 'string') {
            // 尝试解析各种日期格式
            const trimmedValue = dateValue.trim();

            // 检查是否是YYYY-MM-DD格式
            if (/^\d{4}-\d{2}-\d{2}$/.test(trimmedValue)) {
                return trimmedValue;
            }

            // 尝试解析其他格式
            const date = new Date(trimmedValue);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
            }
        }

        // 默认返回今天
        return new Date().toISOString().split('T')[0];

    } catch (error) {
        console.warn('日期格式转换失败，使用当前日期:', dateValue, error);
        return new Date().toISOString().split('T')[0];
    }
}

// 防抖函数和预览缓存（borderSettings已在文件开头定义）
let previewCache = null;

// 绑定导入导出事件
document.addEventListener('DOMContentLoaded', function() {
    // 导出按钮
    document.getElementById('exportBtn').addEventListener('click', exportToExcel);
    
    // 导入按钮
    document.getElementById('importBtn').addEventListener('click', function() {
        document.getElementById('importFile').click();
    });
    
    // 文件选择
    document.getElementById('importFile').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            importFromExcel(file);
        }
        // 清空文件输入，允许重复选择相同文件
        e.target.value = '';
    });
    
    // 边框设置相关事件绑定
    initBorderSettings();
});

// 初始化边框设置
function initBorderSettings() {
    // 加载保存的边框设置
    loadBorderSettings();
    
    // 绑定边框设置按钮事件
    document.getElementById('borderSettingsBtn').addEventListener('click', openBorderModal);
    document.getElementById('applyBorderSettings').addEventListener('click', function() {
        applyBorderSettings();
        closeBorderModal();
    });
    document.getElementById('resetBorderSettings').addEventListener('click', resetBorderSettings);
    document.getElementById('cancelBorderSettings').addEventListener('click', closeBorderModal);
    
    // 绑定实时预览事件（优化版本）
    document.getElementById('borderColor').addEventListener('input', debouncePreviewBorderSettings);
    document.getElementById('borderWidth').addEventListener('input', debouncePreviewBorderSettings);
    document.getElementById('taskHeight').addEventListener('input', debouncePreviewBorderSettings);
    document.getElementById('taskOpacity').addEventListener('input', debouncePreviewBorderSettings);
    document.getElementById('transparentBorder').addEventListener('change', debouncePreviewBorderSettings);

    // 绑定鼠标释放事件用于最终应用
    ['borderColor', 'borderWidth', 'taskHeight', 'taskOpacity', 'transparentBorder'].forEach(id => {
        const element = document.getElementById(id);
        element.addEventListener('mouseup', applyBorderSettings);
        element.addEventListener('mouseleave', applyBorderSettings);
        ['touchend', 'touchcancel'].forEach(event => {
            element.addEventListener(event, applyBorderSettings);
        });
    });
    
    // 更新滑块显示值
    updateSliderValues();
    
    // 初始化列宽调整
    initColumnResizing();
}

// 初始化列宽调整功能
function initColumnResizing() {
    // 创建列宽调整手柄
    addResizeHandles();
    
    // 加载保存的列宽设置
    loadColumnWidths();
}

// 添加列宽调整手柄
function addResizeHandles() {
    const headers = document.querySelectorAll('.task-header th');
    headers.forEach((header, index) => {
        // 为所有列添加调整手柄，包括编号列
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        resizeHandle.dataset.column = index;
        header.style.position = 'relative';
        header.appendChild(resizeHandle);
        
        // 绑定调整事件
        let isResizing = false;
        let startX = 0;
        let startWidth = 0;
        
        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            startX = e.clientX;
            startWidth = header.offsetWidth;
            document.body.style.cursor = 'col-resize';
            document.body.style.userSelect = 'none';
            e.preventDefault();
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isResizing) return;
            
            const deltaX = e.clientX - startX;
            const newWidth = Math.max(20, startWidth + deltaX); // 最小宽度20px
            
            // 更新表头宽度
            header.style.width = newWidth + 'px';
            
            // 更新对应数据列的宽度
            const dataCells = document.querySelectorAll(`#taskTableBody td:nth-child(${index + 1})`);
            dataCells.forEach(cell => {
                cell.style.width = newWidth + 'px';
                cell.style.minWidth = newWidth + 'px';
                cell.style.maxWidth = newWidth + 'px';
            });
            
            // 实时更新任务表格总宽度
            updateTaskTableWidth();
        });
        
        document.addEventListener('mouseup', () => {
            if (!isResizing) return;
            isResizing = false;
            document.body.style.cursor = '';
            document.body.style.userSelect = '';
            
            // 保存列宽设置
            saveColumnWidths();
        });
    });
}

// 保存列宽设置
function saveColumnWidths() {
    const widths = {};
    const headers = document.querySelectorAll('.task-header th');
    headers.forEach((header, index) => {
        widths[index] = header.offsetWidth;
    });
    localStorage.setItem('ganttColumnWidths', JSON.stringify(widths));
}

// 加载列宽设置
function loadColumnWidths() {
    const savedWidths = JSON.parse(localStorage.getItem('ganttColumnWidths')) || {};
    const headers = document.querySelectorAll('.task-header th');
    const dataCells = document.querySelectorAll('#taskTableBody td');
    
    Object.keys(savedWidths).forEach(index => {
        const width = savedWidths[index];
        const header = headers[index];
        const cells = document.querySelectorAll(`#taskTableBody td:nth-child(${parseInt(index) + 1})`);
        
        if (header) {
            header.style.width = width + 'px';
            header.style.minWidth = width + 'px';
            header.style.maxWidth = width + 'px';
        }
        
        cells.forEach(cell => {
            cell.style.width = width + 'px';
            cell.style.minWidth = width + 'px';
            cell.style.maxWidth = width + 'px';
        });
    });
}

// 打开边框设置模态框
function openBorderModal() {
    document.getElementById('borderModal').style.display = 'block';
    loadBorderSettings(); // 加载当前设置
}

// 关闭边框设置模态框
function closeBorderModal() {
    document.getElementById('borderModal').style.display = 'none';
    // 移除递归调用，避免无限循环
}

// 加载边框设置
function loadBorderSettings() {
    document.getElementById('borderColor').value = borderSettings.borderColor;
    document.getElementById('borderWidth').value = borderSettings.borderWidth;
    document.getElementById('taskHeight').value = borderSettings.taskHeight;
    document.getElementById('taskOpacity').value = borderSettings.taskOpacity;
    document.getElementById('transparentBorder').checked = borderSettings.transparentBorder;
    updateSliderValues();
    applyBorderStyles();
}

// 更新滑块显示值
function updateSliderValues() {
    document.getElementById('borderWidthValue').textContent = borderSettings.borderWidth + 'px';
    document.getElementById('taskHeightValue').textContent = borderSettings.taskHeight + 'px';
    document.getElementById('taskOpacityValue').textContent = borderSettings.taskOpacity + '%';
}

// 防抖预览边框设置 - 性能优化关键函数
function debouncePreviewBorderSettings() {
    // 清除之前的防抖定时器
    if (previewDebounceTimer) {
        clearTimeout(previewDebounceTimer);
    }
    
    // 设置新的防抖定时器（300ms延迟）
    previewDebounceTimer = setTimeout(() => {
        previewBorderSettings();
        previewDebounceTimer = null;
    }, 300);
}

// 预览边框设置 - 优化版本
function previewBorderSettings() {
    const newBorderColor = document.getElementById('borderColor').value;
    const newBorderWidth = parseInt(document.getElementById('borderWidth').value);
    const newTaskHeight = parseInt(document.getElementById('taskHeight').value);
    const newTaskOpacity = parseInt(document.getElementById('taskOpacity').value);
    const newTransparentBorder = document.getElementById('transparentBorder').checked;
    
    // 检查设置是否真的发生了变化
    if (previewCache && 
        previewCache.borderColor === newBorderColor &&
        previewCache.borderWidth === newBorderWidth &&
        previewCache.taskHeight === newTaskHeight &&
        previewCache.taskOpacity === newTaskOpacity &&
        previewCache.transparentBorder === newTransparentBorder) {
        return; // 没有变化，跳过更新
    }
    
    // 更新缓存
    previewCache = {
        borderColor: newBorderColor,
        borderWidth: newBorderWidth,
        taskHeight: newTaskHeight,
        taskOpacity: newTaskOpacity,
        transparentBorder: newTransparentBorder
    };
    
    // 更新设置
    borderSettings.borderColor = newBorderColor;
    borderSettings.borderWidth = newBorderWidth;
    borderSettings.taskHeight = newTaskHeight;
    borderSettings.taskOpacity = newTaskOpacity;
    borderSettings.transparentBorder = newTransparentBorder;
    
    updateSliderValues();
    
    // 使用requestAnimationFrame批量更新DOM，减少重排重绘
    requestAnimationFrame(() => {
        applyBorderStyles();
    });
}

// 应用边框样式 - 性能优化版本
function applyBorderStyles() {
    // 批量获取DOM元素，减少查询次数
    const taskBars = document.querySelectorAll('.gantt-task');
    const taskContainers = document.querySelectorAll('.gantt-task-container');
    const taskLabels = document.querySelectorAll('.gantt-task-label');
    const taskRows = document.querySelectorAll('#taskTableBody tr');
    const taskHeaderRows = document.querySelectorAll('.task-header th');
    
    // 先设置CSS变量（一次性更新）
    document.documentElement.style.setProperty('--task-height', `${borderSettings.taskHeight}px`);
    
    // 使用文档片段批量处理任务条样式
    const taskBarStyles = {
        border: borderSettings.transparentBorder ? 'none' : `${borderSettings.borderWidth}px solid ${borderSettings.borderColor}`,
        boxSizing: 'border-box',
        opacity: borderSettings.taskOpacity / 100,
        height: `${borderSettings.taskHeight}px`,
        lineHeight: `${borderSettings.taskHeight}px`
    };
    
    // 批量应用任务条样式
    taskBars.forEach(taskBar => {
        Object.assign(taskBar.style, taskBarStyles);
    });
    
    // 批量应用任务条容器样式
    taskContainers.forEach((container, index) => {
        container.style.height = `${borderSettings.taskHeight}px`;
        container.style.top = `${index * borderSettings.taskHeight}px`;
    });
    
    // 批量应用任务标签样式
    taskLabels.forEach(label => {
        label.style.height = `${borderSettings.taskHeight}px`;
        label.style.lineHeight = `${borderSettings.taskHeight}px`;
    });
    
    // 批量应用任务表格行样式
    taskRows.forEach(row => {
        row.style.height = `${borderSettings.taskHeight}px`;
        const cells = row.querySelectorAll('td');
        cells.forEach(cell => {
            cell.style.height = `${borderSettings.taskHeight}px`;
            cell.style.lineHeight = `${borderSettings.taskHeight}px`;
        });
    });
    
    // 批量应用任务表格头部样式
    taskHeaderRows.forEach(row => {
        row.style.height = `${borderSettings.taskHeight}px`;
        row.style.lineHeight = `${borderSettings.taskHeight}px`;
    });
    
    // 使用requestAnimationFrame确保样式应用后再进行固定行高应用
    requestAnimationFrame(() => {
        applyFixedRowHeight();
    });
}

// 修复高度对齐问题 - 性能优化版本
function fixHeightAlignment() {
    // 如果已经有动画帧在等待，取消它
    if (alignmentAnimationFrame !== null) {
        cancelAnimationFrame(alignmentAnimationFrame);
    }

    // 使用requestAnimationFrame确保在下一帧批量处理
    alignmentAnimationFrame = requestAnimationFrame(() => {
        const taskTableBody = document.getElementById('taskTableBody');
        const ganttChart = document.getElementById('ganttChart');

        if (taskTableBody && ganttChart) {
            const taskRows = taskTableBody.querySelectorAll('tr');
            const ganttTasks = ganttChart.querySelectorAll('.gantt-task-container');

            // 确保行数匹配
            if (taskRows.length === ganttTasks.length && taskRows.length > 0) {
                // 使用边框设置中的任务高度，如果没有设置则使用默认值
                const fixedRowHeight = (borderSettings && borderSettings.taskHeight) ? borderSettings.taskHeight : FIXED_ROW_HEIGHT;

                // 强制重置所有元素的高度和位置，使用精确的像素值
                batchApplyStylesWithPrecision(taskRows, ganttTasks, fixedRowHeight);

                // 设置甘特图容器高度 - 使用精确计算（缩小行高）
                const totalHeight = taskRows.length * fixedRowHeight + 80; // 空白行也使用统一的固定行高
                ganttChart.style.minHeight = `${totalHeight}px`;
                ganttChart.style.height = `${totalHeight}px`;

                // 强制浏览器重新计算布局
                ganttChart.offsetHeight;
                taskTableBody.offsetHeight;

                console.log(`修复滚动对齐: ${taskRows.length}个任务, 固定行高: ${fixedRowHeight}px, 总高度: ${totalHeight}px`);
            }
        }

        alignmentAnimationFrame = null;
    });
}

// 批量应用样式 - 减少DOM操作
function batchApplyStyles(taskRows, ganttTasks, rowHeight) {
    // 使用文档片段进行批量操作
    const styleUpdates = [];

    taskRows.forEach((row, index) => {
        const taskBar = ganttTasks[index];
        if (taskBar) {
            const topPosition = index * rowHeight;

            // 收集样式更新
            styleUpdates.push({
                element: taskBar,
                styles: {
                    height: `${rowHeight}px`,
                    top: `${topPosition}px`
                }
            });

            // 处理任务条内部元素
            const ganttTask = taskBar.querySelector('.gantt-task');
            if (ganttTask) {
                styleUpdates.push({
                    element: ganttTask,
                    styles: {
                        height: `${rowHeight}px`,
                        lineHeight: `${rowHeight}px`,
                        boxSizing: 'border-box'
                    }
                });
            }

            const taskLabel = taskBar.querySelector('.gantt-task-label');
            if (taskLabel) {
                styleUpdates.push({
                    element: taskLabel,
                    styles: {
                        height: `${rowHeight}px`,
                        lineHeight: `${rowHeight}px`,
                        boxSizing: 'border-box'
                    }
                });
            }
        }
    });

    // 批量应用样式更新
    styleUpdates.forEach(({ element, styles }) => {
        Object.assign(element.style, styles);
    });
}

// 精确的跨浏览器样式应用函数
function batchApplyStylesWithPrecision(taskRows, ganttTasks, fixedRowHeight) {
    const styleUpdates = [];

    // 处理任务行
    taskRows.forEach((row, index) => {
        const exactTop = index * fixedRowHeight;

        // 强制设置行样式
        styleUpdates.push({
            element: row,
            styles: {
                height: `${fixedRowHeight}px`,
                lineHeight: `${fixedRowHeight}px`,
                boxSizing: 'border-box',
                margin: '0',
                padding: '0 6px',
                position: 'relative',
                display: 'table-row'
            }
        });

        // 处理行内的单元格
        const cells = row.querySelectorAll('td');
        cells.forEach(cell => {
            // 使用setProperty和!important强制应用高度
            cell.style.setProperty('height', `${fixedRowHeight}px`, 'important');
            cell.style.setProperty('line-height', `${fixedRowHeight}px`, 'important');
            cell.style.setProperty('box-sizing', 'border-box', 'important');
            cell.style.setProperty('vertical-align', 'middle', 'important');
            cell.style.setProperty('padding', '0 6px', 'important');
            cell.style.setProperty('margin', '0', 'important');
        });
    });

    // 处理甘特图任务条容器
    ganttTasks.forEach((taskBar, index) => {
        const exactTop = index * fixedRowHeight;

        // 直接设置甘特图任务条样式，使用!important
        taskBar.style.setProperty('height', `${fixedRowHeight}px`, 'important');
        taskBar.style.setProperty('top', `${exactTop}px`, 'important');
        taskBar.style.setProperty('position', 'absolute', 'important');
        taskBar.style.setProperty('width', '100%', 'important');
        taskBar.style.setProperty('margin', '0', 'important');
        taskBar.style.setProperty('padding', '0', 'important');
        taskBar.style.setProperty('box-sizing', 'border-box', 'important');
        taskBar.style.setProperty('left', '0', 'important');

        // 处理任务条内部元素
        const ganttTask = taskBar.querySelector('.gantt-task');
        if (ganttTask) {
            ganttTask.style.setProperty('height', `${fixedRowHeight}px`, 'important');
            ganttTask.style.setProperty('line-height', `${fixedRowHeight}px`, 'important');
            ganttTask.style.setProperty('top', '0px', 'important');
            ganttTask.style.setProperty('margin', '0', 'important');
            ganttTask.style.setProperty('padding', '0 5px', 'important');
            ganttTask.style.setProperty('box-sizing', 'border-box', 'important');
            ganttTask.style.setProperty('vertical-align', 'middle', 'important');
            ganttTask.style.setProperty('position', 'absolute', 'important');
        }

        const ganttLabel = taskBar.querySelector('.gantt-task-label');
        if (ganttLabel) {
            ganttLabel.style.setProperty('height', `${fixedRowHeight}px`, 'important');
            ganttLabel.style.setProperty('line-height', `${fixedRowHeight}px`, 'important');
            ganttLabel.style.setProperty('top', '0px', 'important');
            ganttLabel.style.setProperty('margin', '0', 'important');
            ganttLabel.style.setProperty('padding', '0 5px', 'important');
            ganttLabel.style.setProperty('box-sizing', 'border-box', 'important');
            ganttLabel.style.setProperty('vertical-align', 'middle', 'important');
            ganttLabel.style.setProperty('position', 'absolute', 'important');
        }
    });

    // 样式已经直接应用，无需批量更新
    console.log(`高度对齐完成: ${taskRows.length}行任务，${ganttTasks.length}个甘特图任务条`);
}

// 应用边框设置
function applyBorderSettings() {
    // 立即清除防抖定时器，应用当前设置
    if (previewDebounceTimer) {
        clearTimeout(previewDebounceTimer);
        previewDebounceTimer = null;
    }

    previewBorderSettings();
    localStorage.setItem('ganttBorderSettings', JSON.stringify(borderSettings));
    // 移除递归调用，由调用者决定是否关闭模态框
}

// 重置边框设置
function resetBorderSettings() {
    borderSettings = {
        borderColor: '#ffffff',
        borderWidth: 1,
        taskHeight: FIXED_ROW_HEIGHT,
        taskOpacity: 100,
        transparentBorder: false
    };
    loadBorderSettings();
    applyBorderStyles();
}

// 确保在所有渲染操作后都应用边框样式
function ensureBorderStylesApplied() {
    // 使用 requestAnimationFrame 确保在DOM更新后应用样式
    requestAnimationFrame(() => {
        applyBorderStyles();
        fixHeightAlignment();
        // 强制同步对齐检查
        forceSyncAlignment();
    });
}

// 强制同步对齐检查 - 解决跨浏览器差异
function forceSyncAlignment() {
    setTimeout(() => {
        const taskTableBody = document.getElementById('taskTableBody');
        const ganttChart = document.getElementById('ganttChart');

        if (taskTableBody && ganttChart) {
            const taskRows = taskTableBody.querySelectorAll('tr');
            const ganttTasks = ganttChart.querySelectorAll('.gantt-task-container');

            if (taskRows.length === ganttTasks.length && taskRows.length > 0) {
                const fixedRowHeight = FIXED_ROW_HEIGHT; // 使用统一的固定行高

                // 最终的精确对齐检查
                taskRows.forEach((row, index) => {
                    const taskBar = ganttTasks[index];
                    if (taskBar) {
                        const expectedTop = index * fixedRowHeight;
                        const currentTop = parseInt(taskBar.style.top) || 0;

                        // 如果位置不准确，强制修正
                        if (Math.abs(currentTop - expectedTop) > 1) {
                            taskBar.style.top = `${expectedTop}px`;
                            taskBar.style.height = `${fixedRowHeight}px`;

                            // 修正内部元素
                            const ganttTask = taskBar.querySelector('.gantt-task');
                            const ganttLabel = taskBar.querySelector('.gantt-task-label');

                            if (ganttTask) {
                                ganttTask.style.height = `${fixedRowHeight}px`;
                                ganttTask.style.lineHeight = `${fixedRowHeight}px`;
                                ganttTask.style.top = '0px';
                            }

                            if (ganttLabel) {
                                ganttLabel.style.height = `${fixedRowHeight}px`;
                                ganttLabel.style.lineHeight = `${fixedRowHeight}px`;
                                ganttLabel.style.top = '0px';
                            }
                        }

                        // 确保行高度也是准确的
                        row.style.height = `${fixedRowHeight}px`;
                        row.style.lineHeight = `${fixedRowHeight}px`;

                        const cells = row.querySelectorAll('td');
                        cells.forEach(cell => {
                            cell.style.height = `${fixedRowHeight}px`;
                            cell.style.lineHeight = `${fixedRowHeight}px`;
                        });
                    }
                });

                console.log(`强制同步对齐完成: ${taskRows.length}个任务, 行高: ${fixedRowHeight}px`);
            }
        }
    }, 100); // 延迟100ms确保所有渲染完成
}

// 重写关键渲染函数，确保边框设置持久化
const originalRenderTaskBars = renderTaskBars;
renderTaskBars = function(startDate, endDate, step, unitWidth) {
    originalRenderTaskBars(startDate, endDate, step, unitWidth);
    ensureBorderStylesApplied();
};

// 确保边框样式在渲染后应用 - 使用事件监听方式避免重复声明
document.addEventListener('tasksRendered', ensureBorderStylesApplied);

// 拖拽功能实现
function addDragListeners(taskBar, task, taskIndex) {
    taskBar.addEventListener('mousedown', function(e) {
        e.preventDefault();
        startDrag(e, taskBar, task, taskIndex);
    });
}

function startDrag(e, taskBar, task, taskIndex) {
    isDragging = true;
    draggedTask = taskBar;
    draggedTaskId = task.id;

    // 记录拖拽开始位置
    dragStartX = e.clientX;
    dragStartY = e.clientY;

    // 获取任务条相对于甘特图的位置
    const ganttChart = document.getElementById('ganttChart');
    const ganttRect = ganttChart.getBoundingClientRect();
    const taskRect = taskBar.getBoundingClientRect();

    dragOffsetX = taskRect.left - ganttRect.left;
    dragOffsetY = taskRect.top - ganttRect.top;

    // 添加拖拽样式
    taskBar.classList.add('dragging');

    // 创建拖拽指示器
    createDropIndicators();

    // 添加全局鼠标事件
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);

    // 防止文本选择
    document.body.style.userSelect = 'none';
}

function handleDragMove(e) {
    if (!isDragging || !draggedTask) return;

    const deltaX = e.clientX - dragStartX;
    const deltaY = e.clientY - dragStartY;

    // 更新任务条位置（视觉反馈）
    draggedTask.style.transform = `translate(${deltaX}px, ${deltaY}px)`;

    // 显示拖拽指示器
    updateDropIndicators(e);
}

function handleDragEnd(e) {
    if (!isDragging || !draggedTask) return;

    // 计算新位置
    const deltaX = e.clientX - dragStartX;
    const deltaY = e.clientY - dragStartY;

    // 执行拖拽操作
    performDrop(deltaX, deltaY);

    // 清理拖拽状态
    cleanupDrag();
}

function createDropIndicators() {
    // 创建行拖拽指示器
    if (!dropIndicator) {
        dropIndicator = document.createElement('div');
        dropIndicator.className = 'drop-indicator';
        document.getElementById('ganttChart').appendChild(dropIndicator);
    }

    // 创建时间轴拖拽指示器
    if (!timeDropIndicator) {
        timeDropIndicator = document.createElement('div');
        timeDropIndicator.className = 'time-drop-indicator';
        document.getElementById('ganttChart').appendChild(timeDropIndicator);
    }
}

function updateDropIndicators(e) {
    const ganttChart = document.getElementById('ganttChart');
    const ganttRect = ganttChart.getBoundingClientRect();

    // 计算相对于甘特图的鼠标位置
    const relativeY = e.clientY - ganttRect.top;
    const relativeX = e.clientX - ganttRect.left;

    // 更新行指示器
    const targetRowIndex = Math.floor(relativeY / FIXED_ROW_HEIGHT);
    const maxRows = tasks.length;

    if (targetRowIndex >= 0 && targetRowIndex < maxRows) {
        dropIndicator.style.top = `${targetRowIndex * FIXED_ROW_HEIGHT}px`;
        dropIndicator.classList.add('active');
    } else {
        dropIndicator.classList.remove('active');
    }

    // 更新时间轴指示器
    if (relativeX >= 0) {
        timeDropIndicator.style.left = `${relativeX}px`;
        timeDropIndicator.classList.add('active');
    } else {
        timeDropIndicator.classList.remove('active');
    }
}

function performDrop(deltaX, deltaY) {
    const task = tasks.find(t => t.id === draggedTaskId);
    if (!task) return;

    const currentIndex = tasks.findIndex(t => t.id === draggedTaskId);

    // 计算新的行位置
    const newRowIndex = Math.floor((dragOffsetY + deltaY) / FIXED_ROW_HEIGHT);
    const maxRows = tasks.length - 1;
    const clampedRowIndex = Math.max(0, Math.min(maxRows, newRowIndex));

    // 计算时间偏移
    const { start: projectStart } = getDateRange();
    const unitWidth = 40; // 基础单位宽度
    const stepDays = getStepDays(); // 获取当前步长
    const unitOffset = Math.round(deltaX / unitWidth); // 单位偏移
    const timeOffset = unitOffset * stepDays; // 天数偏移 = 单位偏移 * 步长

    // 更新任务位置（如果行位置改变）
    if (clampedRowIndex !== currentIndex) {
        // 移动任务到新位置
        const [movedTask] = tasks.splice(currentIndex, 1);
        tasks.splice(clampedRowIndex, 0, movedTask);
    }

    // 更新任务时间（如果有时间偏移）
    if (timeOffset !== 0) {
        const startDate = new Date(task.start);
        const endDate = new Date(task.end);

        startDate.setDate(startDate.getDate() + timeOffset);
        endDate.setDate(endDate.getDate() + timeOffset);

        task.start = startDate.toISOString().split('T')[0];
        task.end = endDate.toISOString().split('T')[0];
    }

    // 保存到localStorage
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));

    // 重新渲染
    renderTasks();
    renderGanttChart();
}

function cleanupDrag() {
    isDragging = false;

    if (draggedTask) {
        draggedTask.classList.remove('dragging');
        draggedTask.style.transform = '';
        draggedTask = null;
    }

    draggedTaskId = null;

    // 隐藏指示器
    if (dropIndicator) {
        dropIndicator.classList.remove('active');
    }
    if (timeDropIndicator) {
        timeDropIndicator.classList.remove('active');
    }

    // 移除全局事件监听器
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);

    // 恢复文本选择
    document.body.style.userSelect = '';
}

// 确保边框样式在渲染后应用 - 使用事件监听方式避免重复声明
document.addEventListener('tasksRendered', ensureBorderStylesApplied);